{"name": "product", "version": "0.1.0", "private": true, "scripts": {"dev": "npm install && next dev", "dev:with-lint-format": "npm run lint && npm run format && next dev", "build": "next build", "start": "next start", "lint": "eslint --fix \"src/**/*.ts\" \"src/**/*.tsx\"", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css}'", "test": "jest", "prepare": "husky", "test:playwright": "npx ts-node tests/saveAuthState.ts && npx playwright test"}, "dependencies": {"@auth0/nextjs-auth0": "^3.5.0", "@floating-ui/react": "^0.27.4", "@google/generative-ai": "^0.24.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@liveblocks/client": "^2.12.2", "@liveblocks/node": "^2.12.2", "@liveblocks/react": "^2.12.2", "@liveblocks/react-tiptap": "^2.12.2", "@liveblocks/react-ui": "^2.12.2", "@neondatabase/serverless": "^0.10.4", "@novu/node": "^2.0.6", "@novu/notification-center": "^2.0.0-canary.0", "@playwright/test": "^1.48.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.0", "@segment/analytics-next": "^1.61.0", "@segment/analytics-node": "^1.1.3", "@sentry/nextjs": "^8.12.0", "@stripe/stripe-js": "^3.4.1", "@tanstack/react-query": "^5.74.3", "@tiptap/extension-character-count": "^2.10.3", "@tiptap/extension-collaboration": "^2.10.3", "@tiptap/extension-collaboration-cursor": "^2.10.3", "@tiptap/extension-highlight": "^2.10.3", "@tiptap/extension-image": "^2.10.3", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-placeholder": "^2.10.3", "@tiptap/extension-task-list": "^2.10.3", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/extension-typography": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/react": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@types/express": "^5.0.1", "auth0": "^4.10.0", "axios": "^1.8.4", "bufferutil": "^4.0.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto": "^1.0.1", "d3": "^7.8.5", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.42.0", "drizzle-zod": "^0.7.1", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "firebase": "^11.0.2", "framer-motion": "^11.13.1", "fuse.js": "^7.0.0", "html2canvas": "^1.4.1", "http-proxy-middleware": "^3.0.5", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "js-cookies": "^1.0.4", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.453.0", "marked": "^15.0.7", "mdast-util-to-hast": "^13.2.0", "next": "^15.2.4", "nextjs-toploader": "^1.6.12", "product": "file:", "react": "^18.3.1", "react-confetti": "^6.2.2", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-force-graph-3d": "^1.24.2", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-modal": "^3.16.1", "react-resizable-panels": "^2.1.4", "react-vega": "^7.6.0", "recharts": "^2.13.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "sonner": "^2.0.2", "stripe": "^15.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.163.0", "tiptap-extension-resize-image": "^1.2.1", "ts-jest": "^29.1.1", "unified": "^11.0.5", "utf-8-validate": "^6.0.5", "vaul": "^1.1.0", "vega": "^5.28.0", "vega-lite": "^5.17.0", "wouter": "^3.3.5", "zod": "^3.23.8", "zod-validation-error": "^3.4.0", "zustand": "^4.5.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.1", "@types/auth0": "^3.3.10", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@types/three": "^0.163.0", "@typescript-eslint/parser": "^7.7.0", "@vitejs/plugin-react": "^4.4.0", "autoprefixer": "^10.4.20", "encoding": "^0.1.13", "eslint": "^8.57.0", "eslint-config-next": "13.5.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "postcss": "^8.4.47", "prettier": "^3.2.5", "resize-observer-polyfill": "^1.5.1", "tailwindcss": "^3.4.14", "ts-node": "^10.9.2", "typescript": "^5.6.3", "vite": "^6.3.2"}}