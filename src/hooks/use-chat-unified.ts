import { useState, useEffect, useCallback } from "react";
import { useMutation } from "@tanstack/react-query";
import { ChatMessage } from "@/lib/api-types";
import { sendChatMessage } from "@/lib/api-client";
import { useToast } from "@/hooks/use-toast";

export interface UseChatOptions {
  /**
   * Whether to enable detailed console logging
   */
  enableLogging?: boolean;
}

/**
 * Unified chat hook that provides chat functionality with experiment context
 * 
 * @param runId - The experiment run ID
 * @param initialContext - The initial context (report text)
 * @param options - Additional options for customizing behavior
 */
export function useChat(
  runId: string, 
  initialContext: string,
  options: UseChatOptions = {}
) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const { toast } = useToast();
  const { enableLogging = false } = options;

  // Logging utility
  const log = useCallback(
    (message: string, data?: any) => {
      if (enableLogging) {
        if (data) {
          console.log(`useChat: ${message}`, data);
        } else {
          console.log(`useChat: ${message}`);
        }
      }
    },
    [enableLogging]
  );

  /**
   * Extract key information from report text
   * This helps create a more informative welcome message
   */
  const extractReportInfo = useCallback((reportText: string) => {
    if (!reportText) return { title: "", summary: "", keyFindings: [] };

    log("Extracting report info");
    // Process report text to extract structured information
    const reportLines = reportText.split("\n");

    // Extract title
    let experimentTitle = "";
    const titlePatterns = [
      /^# (?:CAUSAL INSIGHTS REPORT|Causal Insights Report):\s*(.*?)$/i,
      /^## (?:EXPERIMENT|Study|Analysis):\s*(.*?)$/i,
      /^## (?:TITLE|Experiment Title):\s*(.*?)$/i,
    ];

    for (const line of reportLines) {
      for (const pattern of titlePatterns) {
        const match = line.match(pattern);
        if (match && match[1]) {
          experimentTitle = match[1].trim();
          break;
        }
      }
      if (experimentTitle) break;
    }

    // Extract key findings
    const keyFindings: string[] = [];
    let inKeyFindingsSection = false;

    for (const line of reportLines) {
      // Check if we're entering a key findings section
      if (
        line.match(
          /^## (?:Key Findings|Key Causal Insights|Significant Effects)/i
        )
      ) {
        inKeyFindingsSection = true;
        continue;
      }

      // Check if we're leaving the key findings section
      if (inKeyFindingsSection && line.match(/^## /)) {
        inKeyFindingsSection = false;
        continue;
      }

      // Extract bullet points from key findings section
      if (inKeyFindingsSection && line.match(/^\s*[-*]\s+/)) {
        const finding = line.replace(/^\s*[-*]\s+/, "").trim();
        if (finding && finding.length > 10) {
          keyFindings.push(finding);
        }
      }
    }

    // If no key findings were found in a dedicated section, try to extract from anywhere in the report
    if (keyFindings.length === 0) {
      const impactPatterns = [
        /([^.!?]+(?:increased|decreased|improved|reduced)[^.!?]+by\s+\d+(?:\.\d+)?%[^.!?]*)/gi,
        /([^.!?]+had\s+(?:a|an)\s+(?:significant|strong)\s+(?:positive|negative)\s+impact[^.!?]*)/gi,
      ];

      for (const pattern of impactPatterns) {
        let match;
        const text = reportText;
        while (
          (match = pattern.exec(text)) !== null &&
          keyFindings.length < 5
        ) {
          const finding = match[1].trim();
          if (
            finding &&
            finding.length > 10 &&
            !keyFindings.includes(finding)
          ) {
            keyFindings.push(finding);
          }
        }
      }
    }

    // Limit to 3 key findings
    const limitedFindings = keyFindings.slice(0, 3);

    // Extract or create a summary
    let summary = "";
    let inSummarySection = false;
    const summaryLines: string[] = [];

    for (const line of reportLines) {
      // Check if we're entering a summary section
      if (line.match(/^## (?:Executive Summary|Summary|Overview)/i)) {
        inSummarySection = true;
        continue;
      }

      // Check if we're leaving the summary section
      if (inSummarySection && line.match(/^## /)) {
        inSummarySection = false;
        continue;
      }

      // Collect lines from the summary section
      if (inSummarySection && line.trim() && !line.match(/^#/)) {
        summaryLines.push(line.trim());
      }
    }

    // Use the first few lines of the summary section
    if (summaryLines.length > 0) {
      summary = summaryLines.slice(0, 2).join(" ");
    }

    log("Extracted report info", { title: experimentTitle, keyFindings: limitedFindings.length });
    return {
      title: experimentTitle,
      summary,
      keyFindings: limitedFindings,
    };
  }, [log]);

  // Initialize with welcome message and system message with full report once we have the report
  useEffect(() => {
    // Only run this effect once when initialContext is available and messages are empty
    if (initialContext && messages.length === 0) {
      log("Initial context received", initialContext.substring(0, 100) + "...");

      try {
        // Extract structured information from the report
        const { title, summary, keyFindings } = extractReportInfo(initialContext);

        // Create a system message with the full report content and instructions for detailed responses
        const systemMessage: ChatMessage = {
          role: "system",
          content: `You are a helpful, conversational AI assistant that provides DETAILED, COMPREHENSIVE responses about causal insights experiments. Your responses should be at least 3-4 paragraphs long, with thorough explanations. Use natural language, avoid being overly robotic or terse. Always provide context, examples, and explanations when discussing findings. Be engaging and personable while maintaining accuracy. When making recommendations, provide specific actionable steps with reasoning behind each recommendation. Elaborate on implications and potential outcomes of following your advice.\n\nThis is the full report for the experiment "${title || "Unknown"}": \n\n${initialContext}`
        };

        // Create a more informative welcome message
        let welcomeText = `# 👋 Welcome to the Causal Insights Platform!

I've analyzed the experiment data${title ? ` for **${title}**` : ""} and generated a comprehensive report for you.`;

        // Add summary if available
        if (summary) {
          welcomeText += `\n\n${summary}`;
        }

        // Add key findings if available
        if (keyFindings.length > 0) {
          welcomeText += `\n\n## Key Findings:\n`;
          keyFindings.forEach((finding) => {
            welcomeText += `\n- ${finding}`;
          });
        }

        // Add suggestions for questions
        welcomeText += `\n\n**Ask me anything about:**
- The experimental methodology and design
- Specific causal effects and their significance
- Practical applications of these findings
- Recommendations based on the data
- Suggestions for follow-up experiments

I'm here to help you understand what factors make the most significant impact, and why.`;

        // Set messages with system message and welcome message
        setMessages([
          systemMessage,
          {
            role: "assistant",
            content: welcomeText.trim(),
          },
        ]);
      } catch (error) {
        console.error("Error creating welcome message:", error);

        // Create a system message with the full report content even in case of error
        const systemMessage: ChatMessage = {
          role: "system",
          content: `This is the full report for the experiment: \n\n${initialContext}`
        };

        // Set a simple welcome message if there's an error, but still include the system message
        setMessages([
          systemMessage,
          {
            role: "assistant",
            content: "# 👋 Welcome to the Causal Insights Platform!\n\nI'm here to help you understand your experiment data. What would you like to know?",
          },
        ]);
      }
    }
  }, [initialContext, extractReportInfo, log, messages.length]);

  // Mutation for sending messages
  const mutation = useMutation({
    mutationFn: async (message: string) => {
      log("mutationFn called with message:", message);

      // Add user message to state immediately
      const userMessage: ChatMessage = {
        role: "user",
        content: message,
      };

      log("Adding user message to state:", userMessage);
      setMessages((prev) => [...prev, userMessage]);

      // Prepare context messages
      const contextMessages = [...messages, userMessage];

      log("Sending message to API with context messages:", contextMessages.length);

      // Send to API with run_id context
      try {
        const response = await sendChatMessage(runId, contextMessages);
        log("API response:", response);
        return response;
      } catch (error) {
        log("Error sending message to API:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      // Add the assistant's response to messages
      if (data.message) {
        setMessages((prev) => [...prev, data.message]);
      }

      // Show error toast if there was an API error
      if (data.error) {
        toast({
          title: "Error processing message",
          description: data.error,
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error("Chat error:", error);

      // Create a more helpful error message
      let errorMessage =
        "I'm sorry, I encountered an error processing your request.";

      // Add specific error details if available
      if (error instanceof Error) {
        // Check for common error types and provide more helpful messages
        if (
          error.message.includes("timeout") ||
          error.message.includes("network")
        ) {
          errorMessage +=
            " There seems to be a network issue. Please check your connection and try again.";
        } else if (
          error.message.includes("permission") ||
          error.message.includes("access")
        ) {
          errorMessage +=
            " You may not have permission to access this data. Please check your credentials.";
        } else if (
          error.message.includes("not found") ||
          error.message.includes("404")
        ) {
          errorMessage +=
            " The requested data could not be found. Please verify the experiment ID.";
        } else {
          errorMessage += ` ${error.message}`;
        }
      } else {
        errorMessage +=
          " Please try again or contact support if the issue persists.";
      }

      // Add error message to chat
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: errorMessage,
        },
      ]);

      // Show error toast with more specific information
      toast({
        title: "Error Processing Request",
        description:
          error instanceof Error
            ? `${error.message}. Please try again.`
            : "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Send a message
  const sendMessage = (message: string) => {
    mutation.mutate(message);
  };

  return {
    messages,
    isLoading: mutation.isPending,
    sendMessage,
  };
}
