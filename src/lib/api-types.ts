/**
 * API Types - Shared between client and server
 */

export interface RunIdRequest {
  run_id: string;
}

export interface ExperimentDefinitionData {
  title?: string;
  context?: string;
  target_behavior?: string;
  why_prompt?: string;
  country?: string;
  state?: string;
  year?: number;
  respondent_dependent_variable?: string;
  [key: string]: any;
}

export interface AnalyticsOutputData {
  [key: string]: any;
}

export interface ArtifactResponse {
  success: boolean;
  experiment_definition_file: string;
  analytics_output_file: string;
  experiment_definition_data: ExperimentDefinitionData;
  analytics_output_data?: AnalyticsOutputData;
  error?: string;
}

export interface ReportRequest {
  run_id: string;
  format?: "text" | "pdf";
}

export interface TextReportResponse {
  success: boolean;
  report_text: string;
  pdfUrl?: string; // URL to PDF file if report is in PDF format
  error?: string;
}

export interface Feature {
  attribute: string;
  level: string;
  amce: number;
}

export interface AMCEData {
  DependentVariable: string;
  Features: Feature[];
}

export interface MindsetItem {
  mindset: string;
  attribute_text: string;
  level_text: string;
  row: number;
  AMCE: number;
}

export interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

export interface ChatRequest {
  run_id: string;
  messages: ChatMessage[];
}

export interface ChatResponse {
  success: boolean;
  message: ChatMessage;
  error?: string;
}
