import { apiRequest } from "./queryClient";
import {
  RunIdRequest,
  ArtifactResponse,
  ReportRequest,
  TextReportResponse,
  ChatRequest,
  ChatResponse,
  ChatMessage,
} from "./api-types";

/**
 * Enhanced API Client
 * Provides a more robust interface for API interactions with better error handling
 */
export class ApiClient {
  /**
   * Fetch experiment artifacts by run_id
   * @param runId - The experiment run ID
   * @returns Promise resolving to artifact response
   */
  static async fetchExperimentArtifacts(
    runId: string
  ): Promise<ArtifactResponse> {
    try {
      // Use the v3 API endpoint
      const response = await apiRequest(
        "GET",
        `/api/v3/runs/${runId}/artifacts`,
        undefined
      );
      return await response.json();
    } catch (error) {
      console.error("Error fetching experiment artifacts from v3 API:", error);
      return this.handleApiError<ArtifactResponse>(
        error,
        {
          success: false,
          experiment_definition_file: "",
          analytics_output_file: "",
          experiment_definition_data: {},
        },
        "Error fetching experiment artifacts"
      );
    }
  }

  /**
   * Generate a causal insights report
   * @param runId - The experiment run ID
   * @param format - Optional format (text or pdf)
   * @returns Promise resolving to report response
   */
  static async generateReport(
    runId: string,
    format: "text" | "pdf" = "text"
  ): Promise<TextReportResponse> {
    try {
      // Use the v3 API endpoint
      const response = await apiRequest("POST", `/api/v3/runs/${runId}/report`, {
        format,
      });

      return await response.json();
    } catch (error) {
      console.error("Error generating report from v3 API:", error);
      return this.handleApiError<TextReportResponse>(
        error,
        {
          success: false,
          report_text: "",
        },
        "Error generating report"
      );
    }
  }

  /**
   * Send a message to the chat API with experiment context
   * @param runId - The experiment run ID
   * @param messages - Array of chat messages
   * @returns Promise resolving to chat response
   */
  static async sendChatMessage(
    runId: string,
    messages: ChatMessage[]
  ): Promise<ChatResponse> {
    try {
      // Validate input parameters
      if (!runId) {
        throw new Error("Run ID is required");
      }
      
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        throw new Error("At least one message is required");
      }
      
      // Use the v3 API endpoint
      const response = await apiRequest("POST", `/api/v3/chat`, {
        run_id: runId,
        messages,
      } as ChatRequest);

      return await response.json();
    } catch (error) {
      console.error("Error sending chat message to v3 API:", error);
      
      // Create a more helpful error message based on the error type
      let errorMessage = "An error occurred while processing your request.";
      
      if (error instanceof Error) {
        if (error.message.includes("timeout") || error.message.includes("network")) {
          errorMessage = "Network timeout. Please check your connection and try again.";
        } else if (error.message.includes("permission") || error.message.includes("access")) {
          errorMessage = "You may not have permission to access this data. Please check your credentials.";
        } else if (error.message.includes("not found") || error.message.includes("404")) {
          errorMessage = "The requested data could not be found. Please verify the experiment ID.";
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        message: {
          role: "assistant",
          content: `I'm sorry, I encountered an error processing your request. ${errorMessage}`,
        },
        error: errorMessage,
      };
    }
  }

  /**
   * Test API connection
   * @returns Promise resolving to connection status
   */
  static async testConnection(): Promise<{
    status: string;
    message: string;
  }> {
    try {
      const response = await fetch("/api/test-connection", {
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`API returned status ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Connection test failed:", error);
      return {
        status: "error",
        message: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Generic error handler for API requests
   * @param error - The error that occurred
   * @param defaultValue - Default value to return
   * @param prefix - Prefix for the error message
   * @returns The default value with error information
   */
  private static handleApiError<T extends { success: boolean; error?: string }>(
    error: unknown,
    defaultValue: T,
    prefix: string
  ): T {
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return {
      ...defaultValue,
      success: false,
      error: `${prefix}: ${errorMessage}`,
    };
  }
}

// Export individual functions for backward compatibility
export const {
  fetchExperimentArtifacts,
  generateReport,
  sendChatMessage,
  testConnection,
} = ApiClient;
