/**
 * Scroll Utilities
 * Functions for handling scrolling behavior consistently
 */

/**
 * Scroll an element into view with smooth behavior
 * @param element - The element to scroll into view
 */
export function scrollElementIntoView(element: HTMLElement | null): void {
  if (!element) return;
  
  try {
    // Use scrollIntoView with smooth behavior
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });
  } catch (error) {
    // Fallback for browsers that don't support smooth scrolling
    element.scrollIntoView(false);
  }
}

/**
 * Scroll to the bottom of a container
 * @param container - The container to scroll to the bottom of
 */
export function scrollToBottom(container: HTMLElement | null): void {
  if (!container) return;
  
  try {
    container.scrollTop = container.scrollHeight;
  } catch (error) {
    console.error('Error scrolling to bottom:', error);
  }
}

/**
 * Scroll to the bottom of a container with a delay
 * @param container - The container to scroll to the bottom of
 * @param delay - The delay in milliseconds
 * @returns A function to cancel the delayed scroll
 */
export function scrollToBottomWithDelay(
  container: HTMLElement | null,
  delay: number = 100
): () => void {
  if (!container) return () => {};
  
  const timeoutId = setTimeout(() => {
    scrollToBottom(container);
  }, delay);
  
  return () => clearTimeout(timeoutId);
}

/**
 * Create a robust scroll to bottom function that handles various edge cases
 * @param messagesEndRef - Reference to the element at the end of messages
 * @param containerRef - Reference to the container element
 * @returns A function that scrolls to the bottom
 */
export function createScrollToBottom(
  messagesEndRef: React.RefObject<HTMLElement>,
  containerRef: React.RefObject<HTMLElement>
): () => void {
  return () => {
    // First try to scroll the end element into view
    if (messagesEndRef.current) {
      scrollElementIntoView(messagesEndRef.current);
    }
    
    // Also scroll the container to the bottom as a fallback
    if (containerRef.current) {
      scrollToBottom(containerRef.current);
    }
  };
}
