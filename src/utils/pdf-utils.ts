/**
 * PDF Utilities
 * Functions for handling PDF content and operations
 */

/**
 * Check if content is a PDF based on the PDF header signature
 * @param content - The content to check
 * @returns boolean indicating if the content is a PDF
 */
export function isPdfContent(content: string): boolean {
  return content.startsWith('%PDF');
}

/**
 * Create a downloadable PDF from content
 * @param content - The PDF content as a string
 * @param filename - The filename to use for the download
 */
export function downloadPdfFromContent(content: string, filename: string): void {
  if (!isPdfContent(content)) {
    console.error('Content is not a PDF');
    return;
  }

  try {
    // Create a blob from the PDF content
    const blob = new Blob([content], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    
    // Create a link element
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    
    // Append the link to the document
    document.body.appendChild(a);
    
    // Click the link to trigger the download
    a.click();
    
    // Remove the link from the document
    document.body.removeChild(a);
    
    // Release the blob URL
    URL.revokeObjectURL(url);
    
    console.log(`Downloaded PDF: ${filename}`);
  } catch (error) {
    console.error('Error downloading PDF from content:', error);
  }
}

/**
 * Open PDF content in a new tab
 * @param content - The PDF content as a string or a URL to a PDF
 */
export function openPdfInNewTab(content: string): void {
  try {
    // If content is a URL, open it directly
    if (content.startsWith('http://') || content.startsWith('https://')) {
      window.open(content, '_blank');
      return;
    }
    
    // If content is PDF data, create a blob and open it
    if (isPdfContent(content)) {
      // Convert the text to a Uint8Array of bytes if needed
      let pdfData: BlobPart = content;
      if (typeof content === 'string') {
        const encoder = new TextEncoder();
        pdfData = encoder.encode(content);
      }
      
      // Create a blob from the bytes
      const blob = new Blob([pdfData], { type: 'application/pdf' });
      const pdfUrl = URL.createObjectURL(blob);
      
      // Open the PDF in a new tab
      window.open(pdfUrl, '_blank');
      console.log('Opened PDF in new tab');
    } else {
      console.error('Content is not a PDF and not a URL');
    }
  } catch (error) {
    console.error('Error opening PDF:', error);
  }
}

/**
 * Download a report as text or PDF
 * @param content - The report content
 * @param runId - The experiment run ID
 * @param isPdf - Whether the content is a PDF
 */
export function downloadReport(content: string, runId: string, isPdf: boolean = false): void {
  if (!content) {
    console.error('No content to download');
    return;
  }
  
  try {
    // Handle PDF content
    if (isPdf || isPdfContent(content)) {
      downloadPdfFromContent(content, `Causal_Report_${runId.substring(0, 8)}.pdf`);
      return;
    }
    
    // Handle text content
    const blob = new Blob([content], { type: "text/plain" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `Causal_Report_${runId.substring(0, 8)}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    console.log("Downloaded text report");
  } catch (error) {
    console.error('Error downloading report:', error);
  }
}
