import { ChatMessage } from "@/lib/api-types";

/**
 * Message Utilities
 * Functions for processing and formatting chat messages
 */

/**
 * Process assistant message content to improve formatting and readability
 * @param content - The message content to process
 * @returns Processed content with improved formatting
 */
export function processAssistantMessageContent(content: string): string {
  if (!content) return "";

  let processedContent = content;

  // Step 1: Standardize headings and remove duplicates
  // Handle "Causal Insights Report" headings specifically
  let foundCausalInsightsHeader = false;

  // Array of regex patterns for different heading formats
  const causalInsightsHeaders = [
    /^# CAUSAL INSIGHTS REPORT.*$/gm,
    /^# Causal Insights Report.*$/gm,
    /^## CAUSAL INSIGHTS REPORT.*$/gm,
    /^## Causal Insights Report.*$/gm,
  ];

  // Process each heading pattern
  for (const pattern of causalInsightsHeaders) {
    // Use exec to find matches one by one
    let match;
    const regex = new RegExp(pattern); // Create a new regex for each iteration to reset lastIndex
    while ((match = regex.exec(processedContent)) !== null) {
      const fullMatch = match[0];

      if (!foundCausalInsightsHeader) {
        // Standardize the first occurrence
        processedContent = processedContent.replace(fullMatch, "## Causal Insights Report");
        foundCausalInsightsHeader = true;
      } else {
        // Remove duplicate headings
        processedContent = processedContent.replace(fullMatch, "");
      }
    }
  }

  // Step 2: Enhance formatting for better readability

  // Format lists consistently
  processedContent = processedContent.replace(/^\s*[-*]\s+/gm, "- "); // Standardize list markers

  // Ensure proper spacing around headings
  processedContent = processedContent.replace(/^(#+\s+.*?)(\n[^#\n])/gm, "$1\n$2");

  // Fix excessive line breaks
  processedContent = processedContent.replace(/\n{3,}/g, "\n\n");

  // Step 3: Enhance key information

  // Make key terms and numbers stand out
  processedContent = processedContent.replace(
    /\b(impact|effect|significant|p-value|coefficient|preference|increased|decreased|mindset)\b/gi,
    "**$1**"
  );

  // Highlight percentages and statistical values
  processedContent = processedContent.replace(/\b(\d+(\.\d+)?%)\b/g, "**$1**");

  // Step 4: Format data points and statistics for better readability
  
  // Format price ranges
  processedContent = processedContent.replace(
    /\$(\d+)-\$(\d+)/g, 
    "**$$$1-$$2**"
  );

  // Format statistical findings with better structure
  processedContent = processedContent.replace(
    /([^.!?]+increased[^.!?]+by\s+\+?\d+(?:\.\d+)?%[^.!?]*)/gi,
    (match) => `\n- ${match}`
  );
  
  processedContent = processedContent.replace(
    /([^.!?]+decreased[^.!?]+by\s+-?\d+(?:\.\d+)?%[^.!?]*)/gi,
    (match) => `\n- ${match}`
  );

  // Step 5: Format mindset data more clearly
  
  // Add bullet points for mindset descriptions
  processedContent = processedContent.replace(
    /\b(Social Sippers|Alertness Aficionados|Flavor-First Value|Health-Conscious)[^,.!?]+([\d.]+%)/gi,
    "\n- **$1**$2"
  );

  // Step 6: Clean up any formatting artifacts
  
  // Remove duplicate bullet points
  processedContent = processedContent.replace(/\n-\s+\n-\s+/g, "\n- ");
  
  // Ensure consistent spacing after bullet points
  processedContent = processedContent.replace(/\n-\s{2,}/g, "\n- ");

  return processedContent;
}

/**
 * Check if a message contains a report
 * @param message - The message to check
 * @returns boolean indicating if the message contains a report
 */
export function messageContainsReport(message: ChatMessage): boolean {
  if (message.role !== "assistant" || !message.content) return false;
  
  return (
    message.content.includes("# Causal Insights Report") ||
    message.content.includes("## Causal Insights Report") ||
    message.content.includes("# CAUSAL INSIGHTS REPORT")
  );
}

/**
 * Extract experiment title from report text
 * @param reportText - The report text to extract the title from
 * @returns The extracted title or a default value
 */
export function extractExperimentTitle(reportText: string): string {
  if (!reportText) return "Causal Insights Analysis";
  
  // Try to get a more descriptive title, which may be in different places
  let title = "";

  // First try to extract from "CAUSAL INSIGHTS REPORT: Title" format
  const titleFromHeader = reportText.match(
    /# (?:CAUSAL INSIGHTS REPORT|Causal Insights Report):\s*(.*?)(?:\n|$)/i
  );
  if (titleFromHeader && titleFromHeader[1]) {
    title = titleFromHeader[1].trim();
  } else {
    // Then try to find "What..." patterns which often contain the full title
    const matchTitle = reportText.match(/What[\w\s\d,'":()]+?(?=[.?!])/i);
    if (matchTitle && matchTitle[0]) {
      title = matchTitle[0].trim();
    }
  }

  // If still no good title, use a default
  if (!title) {
    title = "Causal Insights Analysis";
  }

  // Format the title by replacing underscores with spaces and capitalizing words
  title = title
    .replace(/_/g, " ")
    .split(" ")
    .map(
      (word: string) =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    .join(" ");

  return title;
}

/**
 * Create a system message with instructions and context
 * @param reportText - The report text to include in the system message
 * @param title - Optional title to include in the system message
 * @returns A system message with instructions and context
 */
export function createSystemMessage(reportText: string, title?: string): ChatMessage {
  const experimentTitle = title || extractExperimentTitle(reportText);
  
  return {
    role: "system",
    content: `You are a helpful, conversational AI assistant that provides DETAILED, COMPREHENSIVE responses about causal insights experiments. Your responses should be at least 3-4 paragraphs long, with thorough explanations. Use natural language, avoid being overly robotic or terse. Always provide context, examples, and explanations when discussing findings. Be engaging and personable while maintaining accuracy. When making recommendations, provide specific actionable steps with reasoning behind each recommendation. Elaborate on implications and potential outcomes of following your advice.\n\nThis is the full report for the experiment "${experimentTitle}": \n\n${reportText}`
  };
}
