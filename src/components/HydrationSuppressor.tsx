import React, { useEffect, useState } from 'react';

interface HydrationSuppressorProps {
  children: React.ReactNode;
  tag?: keyof JSX.IntrinsicElements;
  className?: string;
}

/**
 * A component that suppresses React hydration warnings.
 * Use this component to wrap elements that might have attributes added by browser extensions.
 *
 * Note: For more complex scenarios where browser extensions add attributes to deeply nested elements,
 * you may need to add suppressHydrationWarning={true} directly to those specific elements.
 */
export function HydrationSuppressor({
  children,
  tag = 'div',
  className = ''
}: HydrationSuppressorProps) {
  // Use client-side only rendering to avoid hydration issues
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Create the element with the specified tag
  const Component = tag as any;

  // During SSR or before hydration, render a placeholder with suppressHydrationWarning
  if (!isMounted) {
    return (
      <Component suppressHydrationWarning className={className}>
        {children}
      </Component>
    );
  }

  // After hydration, render normally
  return (
    <Component suppressHydrationWarning className={className}>
      {children}
    </Component>
  );
}

/**
 * A higher-order component that wraps a component with HydrationSuppressor.
 * @param Component The component to wrap
 * @returns A new component that suppresses hydration warnings
 */
export function withHydrationSuppression<P extends object>(Component: React.ComponentType<P>) {
  return function WrappedComponent(props: P) {
    return (
      <HydrationSuppressor>
        <Component {...props} />
      </HydrationSuppressor>
    );
  };
}
