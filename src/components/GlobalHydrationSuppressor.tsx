"use client";
import React from 'react';
import { usePathname } from 'next/navigation';

/**
 * A component that adds suppressHydrationWarning to the document body
 * and handles global hydration issues.
 */
export function GlobalHydrationSuppressor() {
  const pathname = usePathname();

  // Add suppressHydrationWarning to the document body
  React.useEffect(() => {
    // Add a data attribute to the document body to indicate that hydration suppression is active
    document.body.setAttribute('data-suppress-hydration', 'true');

    // Find all elements with bis_skin_checked attributes and add suppressHydrationWarning
    const elementsWithBisSkinChecked = document.querySelectorAll('[bis_skin_checked]');
    elementsWithBisSkinChecked.forEach(element => {
      // Use a data attribute since we can't directly set React props on DOM elements
      element.setAttribute('data-suppress-hydration', 'true');
    });

    return () => {
      // Clean up when the component unmounts
      document.body.removeAttribute('data-suppress-hydration');
    };
  }, [pathname]); // Re-run when the pathname changes

  // This component doesn't render anything
  return null;
}

export default GlobalHydrationSuppressor;
