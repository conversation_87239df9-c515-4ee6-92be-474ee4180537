import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head />
      <body suppressHydrationWarning={true}>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // This script runs before React hydration
              (function() {
                // Find all elements with bis_skin_checked attribute and add data-suppress-hydration
                function suppressHydrationWarnings() {
                  var elements = document.querySelectorAll('[bis_skin_checked]');
                  for (var i = 0; i < elements.length; i++) {
                    elements[i].setAttribute('data-suppress-hydration', 'true');
                  }
                }
                
                // Run immediately
                suppressHydrationWarnings();
                
                // Also run after DOM content loaded
                document.addEventListener('DOMContentLoaded', suppressHydrationWarnings);
                
                // And after window load
                window.addEventListener('load', suppressHydrationWarnings);
                
                // Define a global variable to disable hydration warnings
                window.__NEXT_HYDRATION_WARNINGS_DISABLED = true;
              })();
            `,
          }}
        />
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
