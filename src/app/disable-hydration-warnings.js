// This script disables hydration warnings in the console
if (typeof window !== 'undefined') {
  // Store the original console.error function
  const originalConsoleError = console.error;
  
  // Override console.error to filter out hydration warnings
  console.error = function(...args) {
    // Check if the error message is related to hydration
    const errorMessage = args.join(' ');
    if (
      errorMessage.includes('Hydration failed because') ||
      errorMessage.includes('Warning: Text content did not match') ||
      errorMessage.includes('Warning: Expected server HTML to contain') ||
      errorMessage.includes('Warning: An error occurred during hydration') ||
      errorMessage.includes('Warning: There was an error while hydrating') ||
      errorMessage.includes('bis_skin_checked')
    ) {
      // Ignore hydration warnings
      return;
    }
    
    // Call the original console.error for other errors
    originalConsoleError.apply(console, args);
  };
  
  // Define a global variable to indicate that hydration warnings are disabled
  window.__NEXT_HYDRATION_WARNINGS_DISABLED = true;
}
