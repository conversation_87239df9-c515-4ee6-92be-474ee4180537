import { handleAuth, handleLogin } from "@auth0/nextjs-auth0";

export async function GET(req, { params }) {
  const handler = handleAuth({
    login: handleLogin({
      authorizationParams: {
        audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,
        scope: "openid profile email offline_access",
      },
    }),
  });

  // We need to properly await the params before accessing them
  const paramsData = await params;
  const auth0 = paramsData.auth0;
  return handler(req, { params: { auth0 } });
}
