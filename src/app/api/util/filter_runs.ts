// Import Run type from a location that exists
// Since we couldn't find the exact location, we'll define a minimal interface here
interface Run {
  id: string;
  name?: string;
  state?: string;
  r_squared?: number;
  sample_size?: number;
  total_number_of_tasks?: number;
  tasks_per_respondent?: number;
  expr_llm_model?: string;
  created_at: string | Date;
  why_prompt?: string;
  confidence_level?: any;
  survey_prompt?: string;
  amce_filename?: string;
  is_private?: boolean;
  [key: string]: any;
}

function filterRuns(runs: Run[] | undefined | any): Run[] {
  // Check if runs is undefined, null, empty, or not an array
  if (!runs || !Array.isArray(runs) || runs.length === 0) {
    console.warn("filterRuns received invalid input:", runs);
    return [];
  }

  return runs.map((run: Run) => {
    return {
      id: run.id,
      name: run.name,
      state: run.state,
      failed: run.state === "finished" && !("r_squared" in run),
      r_squared: run.r_squared ?? 0,
      sample_size: run.sample_size ?? 0,
      total_number_of_tasks: run.total_number_of_tasks ?? 0,
      tasks_per_respondent: run.tasks_per_respondent ?? 0,
      expr_llm_model: run.expr_llm_model ?? "gpt4",
      created_at: run.created_at, // Keep as string
      question: run.why_prompt ?? "",
      task_count: run.total_number_of_tasks ?? 0,
      confidence: run.confidence_level,
      survey_prompt: run.survey_prompt ?? "",
      amce_filename: run.amce_filename,
      is_private: run.is_private ?? false,
    };
  });
}

export default filterRuns;
