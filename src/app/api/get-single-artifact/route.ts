import { NextRequest, NextResponse } from "next/server";
import { getSingleArtifact } from "@/app/interpreter/doc-chat-server/api-integration";
import { initPromise } from "@/app/api/_doc-chat-init";
import { getAccessToken } from "@auth0/nextjs-auth0";

export async function POST(req: NextRequest) {
  try {
    // Wait for the doc-chat server to initialize
    await initPromise;

    const body = await req.json();
    const { file_name } = body;

    if (!file_name) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameter: file_name"
        },
        { status: 400 }
      );
    }

    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    const artifactData = await getSingleArtifact(file_name);

    // Ensure proper JSON formatting
    return NextResponse.json(artifactData);
  } catch (error) {
    console.error("Error fetching single artifact:", error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to fetch artifact: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    );
  }
}
