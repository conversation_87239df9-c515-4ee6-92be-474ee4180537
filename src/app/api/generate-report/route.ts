import { NextRequest, NextResponse } from "next/server";
import { generateCausalInsightsReport } from "@/app/interpreter/doc-chat-server/api-integration";
import type { ReportRequest } from "@/app/interpreter/doc-chat-types/api-types";
import { initPromise } from "@/app/api/_doc-chat-init";
import axios from "axios";

export async function POST(req: NextRequest) {
  try {
    // Wait for the doc-chat server to initialize
    await initPromise;

    const body = (await req.json()) as ReportRequest;
    const { run_id, format = "text" } = body;

    if (!run_id) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameter: run_id",
        },
        { status: 400 }
      );
    }

    // We're focusing on text reports per requirements
    if (format !== "text") {
      return NextResponse.json(
        {
          success: false,
          error: "Only text format reports are supported",
        },
        { status: 400 }
      );
    }

    // Use the generateCausalInsightsReport function from api-integration.ts
    // This function now only uses the external API
    const reportData = await generateCausalInsightsReport(run_id);

    // Check if the report text is a PDF
    if (reportData.report_text && reportData.report_text.startsWith('%PDF')) {
      console.log("Detected PDF content in report");

      // Return the PDF content directly with the correct content type
      return new NextResponse(reportData.report_text, {
        headers: { "Content-Type": "application/pdf" },
      });
    }

    console.log(
      "Report data:",
      reportData.success,
      reportData.report_text
        ? reportData.report_text.substring(0, 100) + "..."
        : "empty"
    );

    // Ensure proper JSON formatting
    return NextResponse.json({
      success: reportData.success,
      report_text: reportData.report_text,
      error: reportData.error,
    });
  } catch (error) {
    console.error("Error generating causal insights report:", error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to generate report: ${error instanceof Error ? error.message : "Unknown error"}`,
      },
      { status: 500 }
    );
  }
}
