import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  context: { params?: Record<string, string | string[]> } // Adjusted context typing for compatibility
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Get ID from route parameters - properly awaiting params
    const paramsData = await context.params;
    const runID = paramsData ? paramsData.id : null;

    if (!runID) {
      return NextResponse.json({ error: "Missing run ID" }, { status: 400 });
    }

    // Make API call with improved error handling
    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${runID}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Run Details Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          error: ErrorMessage.runData,
          details: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json({ error: ErrorMessage.runData }, { status: 500 });
  }
}) as any; // Temporary Auth0 compatibility fix
