import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  context: { params?: Record<string, string | string[]> }
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Get ID from route parameters
    const paramsData = await context.params;
    const runID = paramsData ? paramsData.id : null;

    if (!runID) {
      return NextResponse.json({ error: "Missing run ID" }, { status: 400 });
    }

    // Parse request body
    let body;
    try {
      body = await req.json();
    } catch (e) {
      body = { format: "text" }; // Default to text format if no body provided
    }

    const format = body?.format || "text";

    // Make API call to generate report
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${runID}/report`,
      { format },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        timeout: 30_000, // 30 seconds for report generation
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Report Generation Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          success: false,
          report_text: "",
          error: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        report_text: "",
        error: ErrorMessage.runData || "Failed to generate report",
      },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
