import { NextRequest, NextResponse } from "next/server";
import { getAuthToken } from "@/app/interpreter/doc-chat-server/api-integration";
import { initPromise } from "@/app/api/_doc-chat-init";

export async function GET(req: NextRequest) {
  try {
    // Wait for the doc-chat server to initialize
    await initPromise;

    // Try to get a real auth token to test authentication
    try {
      const token = await getAuthToken();

      return NextResponse.json({
        status: "success",
        message: "API connection test successful",
        authenticated: !!token,
        api_url: process.env.API_BASE_URL,
        google_ai_available: !!process.env.GOOGLE_API_KEY,
      });
    } catch (authError) {
      console.error("Auth error in test connection:", authError);

      return NextResponse.json({
        status: "error",
        message: "API authentication failed",
        error:
          authError instanceof Error
            ? authError.message
            : "Unknown authentication error",
        api_url: process.env.API_BASE_URL,
        auth_url: process.env.AUTH_URL,
        credentials: !!(process.env.API_USERNAME && process.env.API_PASSWORD),
        google_ai_available: !!process.env.GOOGLE_API_KEY,
      });
    }
  } catch (error) {
    console.error("Error testing API connection:", error);
    return NextResponse.json(
      {
        status: "error",
        message:
          error instanceof Error
            ? error.message
            : "Unknown error testing connection",
        google_ai_available: !!process.env.GOOGLE_API_KEY,
      },
      { status: 500 }
    );
  }
}
