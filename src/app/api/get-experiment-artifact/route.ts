import { NextRequest, NextResponse } from "next/server";
import { getExperimentArtifacts } from "@/app/interpreter/doc-chat-server/api-integration";
import type { RunIdRequest } from "@/app/interpreter/doc-chat-types/api-types";
import { initPromise } from "@/app/api/_doc-chat-init";
import { getAccessToken } from "@auth0/nextjs-auth0";

export async function POST(req: NextRequest) {
  try {
    // Wait for the doc-chat server to initialize
    await initPromise;

    const body = (await req.json()) as RunIdRequest;
    const { run_id } = body;

    if (!run_id) {
      return NextResponse.json(
        {
          success: false,
          experiment_definition_file: "",
          analytics_output_file: "",
          experiment_definition_data: {
            title: "Unknown Experiment",
            context: "No experiment context available",
            target_behavior: "Unknown target behavior",
            country: "Unknown",
            year: new Date().getFullYear()
          },
          error: "Missing required parameter: run_id"
        },
        { status: 400 }
      );
    }

    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    try {
      const artifactData = await getExperimentArtifacts(run_id);

      // Ensure proper JSON formatting
      return NextResponse.json({
        success: artifactData.success,
        experiment_definition_file: artifactData.experiment_definition_file,
        analytics_output_file: artifactData.analytics_output_file,
        experiment_definition_data: artifactData.experiment_definition_data || {
          title: `Experiment ${run_id}`,
          context: "No experiment context available",
          target_behavior: "Unknown target behavior",
          country: "Unknown",
          year: new Date().getFullYear()
        },
        analytics_output_data: artifactData.analytics_output_data,
        error: artifactData.error
      });
    } catch (apiError) {
      console.warn("API error fetching experiment artifacts:", apiError);
      // Return a valid response with minimal data instead of an error
      return NextResponse.json({
        success: false,
        experiment_definition_file: "",
        analytics_output_file: "",
        experiment_definition_data: {
          title: `Experiment ${run_id}`,
          context: "No experiment context available",
          target_behavior: "Unknown target behavior",
          country: "Unknown",
          year: new Date().getFullYear()
        },
        error: apiError instanceof Error ? apiError.message : "Error fetching experiment data"
      });
    }
  } catch (error) {
    console.error("Error processing experiment artifacts request:", error);
    // Return a valid response with minimal data instead of an error status
    return NextResponse.json({
      success: false,
      experiment_definition_file: "",
      analytics_output_file: "",
      experiment_definition_data: {
        title: "Unknown Experiment",
        context: "No experiment context available",
        target_behavior: "Unknown target behavior",
        country: "Unknown",
        year: new Date().getFullYear()
      },
      error: `Failed to process request: ${error instanceof Error ? error.message : "Unknown error"}`
    });
  }
}
