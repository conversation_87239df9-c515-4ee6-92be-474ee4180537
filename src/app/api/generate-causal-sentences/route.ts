import { NextRequest, NextResponse } from "next/server";
import { generateCausalSentencesFromAPI } from "@/app/interpreter/doc-chat-server/api-integration";
import type { RunIdRequest } from "@/app/interpreter/doc-chat-types/api-types";
import { initPromise } from "@/app/api/_doc-chat-init";
import { getAccessToken } from "@auth0/nextjs-auth0";

export async function POST(req: NextRequest) {
  try {
    // Wait for the doc-chat server to initialize
    await initPromise;

    const body = (await req.json()) as RunIdRequest;
    const { run_id } = body;

    if (!run_id) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameter: run_id"
        },
        { status: 400 }
      );
    }

    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    const causalSentencesData = await generateCausalSentencesFromAPI(run_id);

    // Ensure proper JSON formatting
    return NextResponse.json(causalSentencesData);
  } catch (error) {
    console.error("Error generating causal sentences:", error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to generate causal sentences: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    );
  }
}
