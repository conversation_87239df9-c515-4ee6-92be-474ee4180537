import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { getAuthToken } from "@/app/interpreter/doc-chat-server/api-integration";

/**
 * Proxy for the v3 chat API endpoint
 * This directly forwards the request to the backend API
 */
export async function POST(req: NextRequest) {
  try {
    // Get the request body
    const body = await req.json();
    const { run_id, messages } = body;

    if (!run_id || !messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameters: run_id and/or messages",
        },
        { status: 400 }
      );
    }

    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for v3 chat API request");

    // Determine the API base URL
    const USE_LOCAL_API = process.env.LOCAL_API_MODE === "true";
    const API_BASE_URL = USE_LOCAL_API
      ? process.env.API_BASE_URL || "http://localhost:8080"
      : process.env.NEXT_PUBLIC_BACKEND_ENDPOINT || "https://api.subconscious.ai";

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when token is available
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    console.log(`Forwarding chat request to ${API_BASE_URL}/api/v3/chat`);

    // Forward the request to the backend API
    const response = await axios.post(
      `${API_BASE_URL}/api/v3/chat`,
      body,
      {
        headers,
        timeout: 30000, // Longer timeout for chat processing
      }
    );

    console.log("Successfully received chat response from v3 API");
    
    // Return the response from the backend API
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error proxying chat request to v3 API:", error);
    
    return NextResponse.json(
      {
        success: false,
        message: {
          role: "assistant",
          content: `I'm sorry, I encountered an error processing your request. ${
            error instanceof Error ? error.message : "Please try again."
          }`
        },
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
