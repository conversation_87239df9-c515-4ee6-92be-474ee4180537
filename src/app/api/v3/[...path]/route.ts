import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { getAuthToken } from "@/app/interpreter/doc-chat-server/api-integration";

/**
 * Catch-all proxy for v3 API endpoints
 * This directly forwards the request to the backend API
 */
export async function GET(
  req: NextRequest,
  context: any
) {
  return handleRequest(req, context.params, "GET");
}

export async function POST(
  req: NextRequest,
  context: any
) {
  return handleRequest(req, context.params, "POST");
}

async function handleRequest(
  req: NextRequest,
  params: { path: string[] },
  method: string
) {
  try {
    // Get auth token
    const token = await getAuthToken();
    console.log(`Obtained token for v3 API ${method} request`);

    // Determine the API base URL
    const USE_LOCAL_API = process.env.LOCAL_API_MODE === "true";
    const API_BASE_URL = USE_LOCAL_API
      ? process.env.API_BASE_URL || "http://localhost:8080"
      : process.env.NEXT_PUBLIC_BACKEND_ENDPOINT || "https://api.subconscious.ai";

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when token is available
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Construct the path
    const apiPath = params.path.join("/");
    const url = `${API_BASE_URL}/api/v3/${apiPath}`;

    console.log(`Forwarding ${method} request to ${url}`);

    // Get the request body for POST requests
    let body = null;
    if (method === "POST") {
      const contentType = req.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        body = await req.json();
      }
    }

    // Forward the request to the backend API
    const response = await axios({
      method,
      url,
      data: body,
      headers,
      timeout: 30000, // Longer timeout
    });

    console.log(`Successfully received response from v3 API: ${url}`);
    
    // Return the response from the backend API
    return NextResponse.json(response.data);
  } catch (error) {
    console.error(`Error proxying ${method} request to v3 API:`, error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
