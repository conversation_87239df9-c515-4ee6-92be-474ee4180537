import { NextRequest, NextResponse } from "next/server";
import type { ChatRe<PERSON> } from "@/app/interpreter/doc-chat-types/api-types";
import { initPromise } from "@/app/api/_doc-chat-init";
import { processExperimentChat } from "@/app/interpreter/doc-chat-server/api-integration";

export async function POST(req: NextRequest) {
  try {
    // Wait for the doc-chat server to initialize
    await initPromise;

    const body = (await req.json()) as ChatRequest;
    const { run_id, messages } = body;

    if (
      !run_id ||
      !messages ||
      !Array.isArray(messages) ||
      messages.length === 0
    ) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameters: run_id and/or messages",
        },
        { status: 400 }
      );
    }

    // Get the last user message
    const lastUserMessage = messages[messages.length - 1];
    const userQuery = lastUserMessage.content;
    
    console.log("Processing chat request for run_id:", run_id);
    console.log("User query:", userQuery);
    
    try {
      // Use the processExperimentChat function from api-integration.ts
      // This function handles both direct API calls and fallback to local processing
      // It also generates a causal insights report to use as context
      const chatResponse = await processExperimentChat(
        run_id,
        userQuery,
        messages.slice(0, -1) // Pass previous messages excluding the current one
      );
      
      console.log("Chat response generated successfully");
      
      return new NextResponse(JSON.stringify(chatResponse), {
        headers: { "Content-Type": "application/json" },
      });
    } catch (apiError) {
      console.error("Error processing chat:", apiError);
      
      return NextResponse.json(
        {
          success: false,
          message: {
            role: "assistant",
            content: `I'm sorry, I encountered an error processing your request. ${
              apiError instanceof Error ? apiError.message : "Please try again."
            }`
          },
          error: apiError instanceof Error ? apiError.message : "Unknown error"
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error processing chat:", error);
    return NextResponse.json(
      {
        success: false,
        message: {
          role: "assistant",
          content: `I'm sorry, I encountered an error processing your request. Please try again.`
        },
        error: `Failed to process chat: ${error instanceof Error ? error.message : "Unknown error"}`,
      },
      { status: 500 }
    );
  }
}
