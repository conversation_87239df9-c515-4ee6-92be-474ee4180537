/**
 * Doc-chat server initialization for Next.js API routes
 * This file is loaded automatically when API routes are accessed
 */

import { getDocChatInitPromise } from "@/app/doc-chat-init";
import { log } from "@/app/interpreter/doc-chat-server/logger";

// Initialize the doc-chat server when this module is imported
log("Initializing doc-chat server from API routes", "api-init");

// Get the initialization promise
const initPromise = getDocChatInitPromise();

// Export the initialization promise for use in API routes
export { initPromise };
