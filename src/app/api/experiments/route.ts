import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import {
  DisplayAttribute,
  DisplayTrait,
  Level,
} from "../../_components/_ideation/objects";

// Helper functions remain unchanged
function formatAttributesAndLevels(displayAttributes: DisplayAttribute[]) {
  const filteredAttributes = displayAttributes.filter(
    (attribute: DisplayAttribute) => {
      return (
        attribute.active === true &&
        attribute.levels.filter((level: Level) => level.active).length >= 2
      );
    }
  );
  return filteredAttributes.map((attribute: DisplayAttribute) => {
    return [
      attribute.attribute,
      attribute.levels
        .filter((level: Level) => level.active)
        .map((level: Level) => level.level),
      attribute.attribute_type,
    ];
  });
}

function formatPopulationTraits(displayTraits: DisplayTrait[]) {
  const activeTraits = displayTraits.filter(
    (trait: DisplayTrait) => trait.active
  );
  const formattedTraits: { [key: string]: string[] } = {};

  activeTraits.forEach((trait: DisplayTrait) => {
    formattedTraits[trait.title] = trait.values;
  });

  return formattedTraits;
}

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: {} // Required for Next.js 15+ compatibility
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Parse request body
    const body = await req.json();

    // Prepare request payload
    const experimentsRequest = {
      pre_cooked_attributes_and_levels_lookup: formatAttributesAndLevels(
        body.displayAttributes
      ),
      realworld_products: body.realworld_products || [],
      population_traits: formatPopulationTraits(body.displayTraits),
      target_population: body.populationTraits,
      why_prompt: body.question,
      country: body.where,
      year: body.when,
      is_private: body.isPrivate,
      state: body.state,
    };

    // Make API call with improved error handling
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/experiments`,
      experimentsRequest,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Experiment Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    return NextResponse.json(
      {
        error: "Failed to create experiment",
        details: axios.isAxiosError(error)
          ? error.response?.data || error.message
          : error instanceof Error
            ? error.message
            : "Unknown error",
      },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
