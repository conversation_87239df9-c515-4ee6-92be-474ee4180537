/**
 * Utility functions for handling serialization issues between Server and Client Components
 */

/**
 * Safely converts any Sets in an object to arrays to prevent serialization errors
 * when passing data from Server Components to Client Components
 */
export function serializeData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }

  // Handle Set objects
  if (data instanceof Set) {
    return Array.from(data);
  }

  // Handle Date objects
  if (data instanceof Date) {
    return data.toISOString();
  }

  // Handle arrays
  if (Array.isArray(data)) {
    return data.map(item => serializeData(item));
  }

  // Handle objects
  if (typeof data === 'object') {
    const result: Record<string, any> = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // Special handling for property 'm' that might be a Set
        if (key === 'm' && data[key] instanceof Set) {
          result[key] = Array.from(data[key]);
        } else {
          result[key] = serializeData(data[key]);
        }
      }
    }
    return result;
  }

  // Return primitive values as is
  return data;
}

/**
 * Deserializes data back to its original form
 * (Currently just returns the data as is, but can be extended if needed)
 */
export function deserializeData(data: any): any {
  return data;
}
