/* eslint-disable no-unused-vars */
//@ts-nocheck
import { serializeData } from "./serialization";

class CustomEventSource {
  private url: string;
  private options: any;
  private eventListeners: { [key: string]: ((event: any) => void)[] };
  private currentEvent: string | undefined;
  private reconnectAttempts: number;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private isConnected: boolean;
  private abortController: AbortController | null;

  constructor(url: string, options: any = {}) {
    this.url = url;
    this.options = options;
    this.eventListeners = {};
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10; // Increased from 5 to 10
    this.reconnectDelay = options.reconnectDelay || 1000;
    this.isConnected = false;
    this.abortController = null;
    
    // Only initialize if URL is provided and not empty
    if (url && url.trim() !== '') {
      this.init();
    } else {
      console.warn("CustomEventSource: No URL provided, event source will not be initialized");
    }
  }

  private async init() {
    try {
      await this.connect();
    } catch (error) {
      this.handleError(error);
    }
  }

  private async connect() {
    // Create a new AbortController for this connection
    this.abortController = new AbortController();
    
    try {
      const response = await fetch(this.url, {
        headers: this.options.headers,
        signal: this.abortController.signal
      });

      if (response.ok) {
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        if (reader) {
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.createEventSource(reader, decoder);
        }
      } else {
        console.warn(`Network response was not ok: ${response.status} ${response.statusText}`);
        // Instead of throwing an error, we'll handle it gracefully
        this.reconnect();
      }
    } catch (error) {
      // Only handle error if it's not an abort error
      if (error.name !== 'AbortError') {
        throw error;
      }
    }
  }

  private createEventSource(
    reader: ReadableStreamDefaultReader<Uint8Array>,
    decoder: TextDecoder
  ) {
    const processText = ({
      done,
      value,
    }: {
      done: boolean;
      value: Uint8Array;
    }) => {
      if (done) {
        this.reconnect();
        return;
      }
      
      try {
        const chunk = decoder.decode(value, { stream: true });
        this.parseChunk(chunk);
        reader.read().then(processText).catch(error => {
          console.error("Error reading from stream:", error);
          this.reconnect();
        });
      } catch (error) {
        console.error("Error processing text:", error);
        this.reconnect();
      }
    };
    
    reader.read().then(processText).catch(error => {
      console.error("Initial read error:", error);
      this.reconnect();
    });
  }

  private parseChunk(chunk: string) {
    if (!chunk || typeof chunk !== 'string') return;
    
    chunk.split("\n").forEach((line) => {
      if (!line) return;
      
      if (line.startsWith("event:")) {
        this.currentEvent = line.replace("event:", "").trim();
      } else if (line.startsWith("data:")) {
        const data = line.replace("data:", "").trim();
        this.dispatchEvent(this.currentEvent || "message", data);
      }
    });
  }

  private dispatchEvent(type: string, data: string) {
    if (!this.eventListeners[type] || !this.eventListeners[type].length) return;
    
    try {
      // Create a safe copy of the data that doesn't contain any Set objects
      const safeData = { data: typeof data === 'string' ? data : JSON.stringify(serializeData(data)) };
      
      // Create a copy of the listeners array to avoid issues if listeners are added/removed during iteration
      const listeners = [...this.eventListeners[type]];
      listeners.forEach(listener => {
        try {
          listener(safeData);
        } catch (error) {
          console.error("Error in event listener:", error);
        }
      });
    } catch (error) {
      console.error("Error dispatching event:", error);
    }
  }

  public addEventListener(type: string, listener: (event: any) => void) {
    if (!type || typeof listener !== 'function') return;
    
    if (!this.eventListeners[type]) {
      this.eventListeners[type] = [];
    }
    
    // Only add the listener if it's not already in the array
    if (!this.eventListeners[type].includes(listener)) {
      this.eventListeners[type].push(listener);
    }
  }

  public removeEventListener(type: string, listener: (event: any) => void) {
    if (!type || !this.eventListeners[type]) return;
    
    const index = this.eventListeners[type].indexOf(listener);
    if (index !== -1) {
      this.eventListeners[type].splice(index, 1);
    }
  }

  public close() {
    this.isConnected = false;
    
    // Abort any ongoing fetch request
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
    
    // Clear all event listeners
    this.eventListeners = {};
  }

  private handleError(error: Error) {
    console.error("CustomEventSource error:", error);
    this.reconnect();
  }

  private reconnect() {
    if (this.isConnected) {
      this.isConnected = false;
    }
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        if (this.abortController) {
          this.abortController.abort();
          this.abortController = null;
        }
        this.init();
      }, delay);
    } else {
      console.warn("Max reconnection attempts reached. Giving up on connecting to:", this.url);
      
      // Dispatch a special error event that the application can listen for
      this.dispatchEvent("error", JSON.stringify({
        type: "connection_failed",
        message: "Max reconnection attempts reached",
        url: this.url
      }));
      
      // If there's an onerror callback, call it
      if (this.options.onerror && typeof this.options.onerror === 'function') {
        try {
          this.options.onerror(new Error("Max reconnection attempts reached"));
        } catch (error) {
          console.error("Error in onerror callback:", error);
        }
      }
    }
  }
}

export default CustomEventSource;
