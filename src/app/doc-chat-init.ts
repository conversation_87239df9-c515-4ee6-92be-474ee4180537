/**
 * Doc-chat server initialization for Next.js
 */

import {
  initDocChatServer,
  shutdownDocChatServer,
} from "./interpreter/doc-chat-server/init";
import { log } from "./interpreter/doc-chat-server/logger";

// Initialize the doc-chat server when the module is imported
let initPromise: Promise<boolean> | null = null;

// Function to get the initialization promise
export function getDocChatInitPromise(): Promise<boolean> {
  if (!initPromise) {
    log("Starting doc-chat server initialization", "next-init");
    initPromise = initDocChatServer().catch((error) => {
      log(`Error initializing doc-chat server: ${error}`, "next-init", "error");
      return false;
    });
  }
  return initPromise;
}

// Function to shutdown the doc-chat server
export function shutdownDocChat(): void {
  log("Shutting down doc-chat server", "next-init");
  shutdownDocChatServer();
  initPromise = null;
}

// Initialize the doc-chat server when the module is imported
getDocChatInitPromise().then((success) => {
  if (success) {
    log("Doc-chat server initialized successfully", "next-init");
  } else {
    log("Doc-chat server initialization failed", "next-init", "error");
  }
});

// Handle process exit to shutdown the doc-chat server
if (typeof process !== "undefined") {
  process.on("exit", () => {
    log("Process exit detected, shutting down doc-chat server", "next-init");
    shutdownDocChat();
  });

  // Handle SIGINT (Ctrl+C)
  process.on("SIGINT", () => {
    log("SIGINT detected, shutting down doc-chat server", "next-init");
    shutdownDocChat();
    process.exit(0);
  });

  // Handle SIGTERM
  process.on("SIGTERM", () => {
    log("SIGTERM detected, shutting down doc-chat server", "next-init");
    shutdownDocChat();
    process.exit(0);
  });
}
