/* eslint-disable prettier/prettier */
"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useUser, withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import countries from "../../../public/data/countries.json";
import ProgressIndicator from "../_components/_ideation/ProgressIndicator";
import WhyComponent from "../_components/_ideation/WhyComponent";
import WhoComponent from "../_components/_ideation/WhoComponent";
import WhatComponent from "../_components/_ideation/WhatComponent";
import SubscribeModal from "../_components/_payments/SubscribeModal";
import {
  Attribute,
  AttributeResponse,
  BrandAttributeCombination,
  Country,
  CreateExperimentRequest,
  DisplayAttribute,
  DisplayTrait,
  FileState,
  Level,
  LLMModel,
  PopulationTraits,
  Trait,
  Trait<PERSON>ategory,
} from "../_components/_ideation/objects";
import WhenWhereComponent from "../_components/_ideation/WhenWhereComponent";
import ExperimentCreationContext from "../_components/_ideation/ExperimentCreationContext";
import { LogRunExperimentEvent } from "../_components/_util/Analytics";
import { Sparkle } from "lucide-react";
import { Header } from "../_components/_ui/NotificationCenter";
import Cookies from "js-cookie";
import Tooltip from "../_components/_util/ToolTip";
import { useAuth0Token } from "../_components/_util/Auth0TokenContext";
import { States } from "../_components/_ideation/_whenwhere/PopulationTraitsData";

const fetcher = async (uri: string, body: CreateExperimentRequest) => {
  const response = await fetch(uri, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(body),
  });
  const data = await response.json();
  return data;
};

interface ExperimentCreationContextType {
  question: string;
  focus: string;
  activeSpecialist: string;
  displayTraits: DisplayTrait[];
  populationTraits: PopulationTraits;
  displayAttributes: DisplayAttribute[];
  realWorlBrandAttributeCombinations: BrandAttributeCombination[];
  when: string;
  where: Country;
  selectedLlmModel: LLMModel;
  selectedState: string | null;
  setSelectedState: (state: string | null) => void;
  setQuestion: (question: string) => void;
  setFocus: (focus: string) => void;
  setActiveSpecialist: (specialist: string) => void;
  setPopulationTraits: (traits: PopulationTraits) => void;
  setDisplayTraits: (traits: DisplayTrait[]) => void;
  setDisplayAttributes: (attributes: DisplayAttribute[]) => void;
  setRealWorldBrandAttributeCombinations: (
    combinations: BrandAttributeCombination[]
  ) => void;
  setWhen: (when: string) => void;
  setWhere: (where: Country) => void;
  setSelectedLlmModel: (model: LLMModel) => void;
  productExists: boolean;
  setProductExists: (exists: boolean) => void;
  attributesData: DisplayAttribute[];
  setAttributesData: (data: DisplayAttribute[]) => void;
}

export default withPageAuthRequired(
  function IdeationPage() {
    const [steps] = useState([
      { name: "Why", status: "current" },
      { name: "When/where", status: "upcoming" },
      { name: "Who", status: "upcoming" },
      { name: "What", status: "upcoming" },
    ]);

    const router = useRouter();
    const searchParams = useSearchParams();
    const { user } = useUser();
    const [isPrivate, setIsPrivate] = useState(false);
    const [runExperiment, setRunExperiment] = useState(false);
    // Why Component
    const [question, setQuestion] = useState("");
    const [focus, setFocus] = useState("engineering");
    const [traits, setTraits] = useState<Record<string, string[]>>({});
    const [isHovered, setIsHovered] = useState(false);
    const [showSubscribeModal, setShowSubscribeModal] =
      useState<boolean>(false);

    const [productExists, setProductExists] = useState(false);

    const [stripeCustomerId, setStripeCustomerId] = useState<string | null>(
      null
    );
    const [renewalDate, setRenewalDate] = useState<string | null>(null);
    const [subscriptionStatus, setSubscriptionStatus] = useState<string | null>(
      null
    );
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    // Who Component
    const [activeSpecialist, setActiveSpecialist] = useState("");
    const [displayTraits, setDisplayTraits] = useState<DisplayTrait[]>([]);

    // What Component
    const [displayAttributes, setDisplayAttributes] = useState<
      DisplayAttribute[]
    >([]);
    const [
      realWorlBrandAttributeCombinations,
      setRealWorldBrandAttributeCombinations,
    ] = useState<BrandAttributeCombination[]>([]);
    const [fileState, setFileState] = useState<FileState>({
      file: null,
      data: null,
      error: null,
    });
    // When and Where Component
    const [when, setWhen] = useState(new Date().getFullYear().toString());
    const [where, setWhere] = useState<Country>(
      countries.find(
        (country) => country.name === "United States of America (USA)"
      )!
    );
    const models: LLMModel[] = [
      { name: "gpt4" },
      { name: "gpt3" },
      // { name: "cohere" },
      // { name: "azure-openai-gpt4" },
      // { name: "azure-cohere-command-r" },
      // { name: "azure-llama-2" },
      // { name: "gcp-haiku" },
      { name: "gcp-sonnet" },
      { name: "gcp-gemini" },
      // { name: "gemini" }
    ];

    const [selectedLlmModel, setSelectedLlmModel] = useState<LLMModel>(
      models[0]
    );
    const [currStep, setCurrStep] = useState(0);
    const [error, setError] = useState<string | null>(null);
    const [roles, setRoles] = useState<string[]>([]); // New state variable for roles
    const [userRunsCount, setUserRunsCount] = useState<number>(0);
    const [canRunExperiments, setCanRunExperiments] = useState<boolean>(true);
    const { auth0AccessToken } = useAuth0Token();
    const [accessToken, setAccessToken] = useState<string | null>(null);
    const [populationTraits, setPopulationTraits] = useState<PopulationTraits>({
      state: null,
      age: [18, 95],
      household_income: [0, 371000],
      gender: ["Female", "Male"],
      education_level: [
        "High School Diploma",
        "High School but no diploma",
        "Some College",
        "Less than high school",
        "Bachelors",
        "Masters",
        "Associates",
        "PhD",
      ],
      number_of_children: ["0", "1", "2", "4+", "3"],
      racial_group: [
        "White",
        "African American",
        "Mixed race",
        "Asian or Pacific Islander",
        "Other race",
      ],
    });
    const [selectedState, setSelectedState] = useState<string | null>(null);
    const [previousStep, setPreviousStep] = useState<number>(-1);
    const [validatedQuestions, setValidatedQuestions] = useState<string[]>([]);
    const [whoSteps, setWhoSteps] = useState<"first" | "second" | "third">(
      previousStep !== 3 ? "first" : "third"
    );
    useEffect(() => {
      const fetchToken = async () => {
        try {
          const response = await fetch("/api/token");
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          const data = await response.json();
          setAccessToken(data.accessToken);
        } catch (error) {
          console.error("Failed to fetch access token:", error);
        }
      };

      fetchToken();
    }, []);

    useEffect(() => {
      const questionFromCookie = Cookies.get("storedQuestion");
      if (questionFromCookie) {
        setQuestion(questionFromCookie);
        Cookies.remove("storedQuestion"); // Remove cookie after retrieving value
      }
    }, []);

    useEffect(() => {
      // When moving to the "what" step, check if populationTraits is undefined
      if (
        currStep === 3 &&
        (!populationTraits || Object.keys(populationTraits).length === 0)
      ) {
        console.warn("Recovering population traits from localStorage");

        // Try to recover from localStorage
        const storedTraits = localStorage.getItem("selectedPopulationTraits");
        if (storedTraits) {
          setPopulationTraits(JSON.parse(storedTraits));
        } else {
          // If not in localStorage, restore default values to prevent undefined errors
          setPopulationTraits({
            state: selectedState || null,
            age: [18, 95],
            household_income: [0, 371000],
            gender: ["Female", "Male"],
            education_level: [
              "High School Diploma",
              "High School but no diploma",
              "Some College",
              "Less than high school",
              "Bachelors",
              "Masters",
              "Associates",
              "PhD",
            ],
            number_of_children: ["0", "1", "2", "4+", "3"],
            racial_group: [
              "White",
              "African American",
              "Mixed race",
              "Asian or Pacific Islander",
              "Other race",
            ],
          });
        }
      }
    }, [currStep, populationTraits, selectedState]);

    useEffect(() => {
      if (user && accessToken) {
        fetch(
          `${process.env.NEXT_PUBLIC_AUTH0_AUDIENCE}users?q=user_id:"${user.sub}"&search_engine=v3`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${auth0AccessToken}`,
            },
          }
        )
          .then((response) => response.json())
          .then((data) => {
            const userData = data[0];
            const { app_metadata } = userData;

            if (app_metadata) {
              if (app_metadata.stripe_customer_id) {
                setStripeCustomerId(app_metadata.stripe_customer_id);
              }
              if (app_metadata.roles) {
                setRoles(app_metadata.roles); // Set roles
              }
            }
          })
          .catch((error) => {
            console.error("Error fetching user data:", error);
          });
      }
    }, [user, accessToken]);

    const checkSubscriptionStatus = async () => {
      if (!stripeCustomerId) {
        console.error("stripeCustomerId is null");
        return;
      }
      try {
        const token = accessToken;

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/check-subscription/?customer_id=${stripeCustomerId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          const errorMessage = await response.text();
          console.error("Error checking subscription:", errorMessage);
          setError(errorMessage);
          return;
        }

        const data = await response.json();
        setSubscriptionStatus(
          data.subscription_status.status === "active" ||
            data.subscription_status.status === "trialing"
            ? "Active"
            : "Not Subscribed"
        );

        const renewalDateInEpoch = data.subscription_status.current_period_end;
        const renewalDate = new Date(renewalDateInEpoch * 1000);

        const formattedRenewalDate = renewalDateInEpoch
          ? renewalDate.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            })
          : null;
        setRenewalDate(formattedRenewalDate);
      } catch (error) {
        console.error("Error checking subscription:", error);
        setError("An error occurred while checking subscription status.");
      }
    };

    const checkUserRunsCount = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/user-runs-count`,
          { method: "GET", headers: { Authorization: `Bearer ${accessToken}` } }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch user runs count");
        }

        const data = await response.json();
        setUserRunsCount(parseInt(data.runs_count));
      } catch (error) {
        console.error("Error fetching user runs count:", error);
      }
    };

    const canRunExperiment = () => {
      const hasActiveSubscription = subscriptionStatus === "Active";
      const isEmployee = roles.includes("employee");
      const isCustomer = roles.includes("customer");
      return (
        userRunsCount < 2 || hasActiveSubscription || isEmployee || isCustomer
      );
    };

    useEffect(() => {
      if (user && accessToken && stripeCustomerId) {
        const fetchData = async () => {
          try {
            await Promise.all([
              checkUserRunsCount(),
              checkSubscriptionStatus(),
            ]);
          } finally {
            // Set the canRunExperiments state variable after fetching the necessary
          }
        };
        fetchData();
      }
    }, [user, accessToken, stripeCustomerId]);

    useEffect(() => {
      setCanRunExperiments(canRunExperiment());
    }, [userRunsCount, subscriptionStatus, roles]);

    function transformTraits(traits: any): DisplayTrait[] {
      const result: DisplayTrait[] = [];

      // Handle each category (usTraits, nonUsTraits, etc.)
      Object.entries(traits).forEach(([category, categoryTraits]) => {
        if (typeof categoryTraits === "object" && categoryTraits !== null) {
          // Handle traits within each category
          Object.entries(categoryTraits as Record<string, string[]>).forEach(
            ([trait, values]) => {
              result.push({
                title: trait.charAt(0).toUpperCase() + trait.slice(1),
                active: false,
                values: Array.isArray(values)
                  ? values.map(
                      (item) => item.charAt(0).toUpperCase() + item.slice(1)
                    )
                  : [],
                category: category as TraitCategory,
              });
            }
          );
        }
      });

      return result;
    }

    function transformAttributes(
      attrs: AttributeResponse[]
    ): DisplayAttribute[] {
      let transformedLevels = attrs.map((attr, AttrIndex) => {
        return attr.levels.map((level) => {
          return {
            level: level,
            active: AttrIndex < 8 ? true : false, // Only show first 8 levels
          };
        });
      });
      return attrs.map((attr, idx) => {
        return {
          attribute: attr.attribute,
          active: idx < 8 ? true : false,
          levels: transformedLevels[idx],
          attribute_type: attr.attribute_type,
        };
      });
    }

    useEffect(() => {
      if (
        traits &&
        Object.keys(traits).length > 0 &&
        displayTraits.length === 0
      ) {
        try {
          const transformed = transformTraits(traits);
          setDisplayTraits(transformed);
        } catch (error) {
          console.error("Error transforming traits:", error);
        }
      }
    }, [traits, displayTraits.length]);

    // useEffect(() => {
    //   if (attributes.length !== 0) {
    //     let transformedAttr = transformAttributes(attributes);
    //     setDisplayAttributes(transformedAttr);
    //   }
    // }, [attributes]);

    function onBack() {
      setPreviousStep(currStep);
      setCurrStep(currStep - 1);
    }

    function completeWhy() {
      steps[0].status = "complete";
      if (steps[1].status !== "complete") steps[1].status = "current";
      setPreviousStep(currStep);
      setCurrStep(1);
    }

    function completeWhenWhere() {
      steps[1].status = "complete";
      if (steps[2].status !== "complete") steps[2].status = "current";
      setPreviousStep(currStep);
      setCurrStep(2);
      if (where.name !== "United States of America (USA)") {
        setWhoSteps("third");
      }
    }

    function completeWho() {
      steps[2].status = "complete";
      if (steps[3].status !== "complete") steps[3].status = "current";
      setPreviousStep(currStep);

      // Add a check to ensure population traits are not undefined before moving to the next step
      if (!populationTraits || Object.keys(populationTraits).length === 0) {
        // If undefined, keep the default values set in the state initialization
        console.warn("Population traits were undefined, using default values");
      }

      setCurrStep(3);
    }

    const setExperimentPrivate = () => {
      setIsPrivate(true);
    };

    const setExperimentPublic = () => {
      setIsPrivate(false);
    };

    const buildRunRequest = async () => {
      if (!question || !when || !where || isPrivate === null || undefined) {
        console.error("One or more fields are missing or invalid.");
        setErrorMessage("One or more fields are missing or invalid.");
        return false;
      }

      // Format displayTraits to strip out category information
      const formattedTraits = displayTraits.map((trait) => ({
        title: trait.title,
        active: trait.active,
        values: trait.values,
      }));

      const requestBody: CreateExperimentRequest = {
        question: question,
        displayTraits: formattedTraits, // Use formatted traits instead of raw displayTraits
        displayAttributes: displayAttributes,
        realworld_products: realWorlBrandAttributeCombinations,
        populationTraits: populationTraits,
        when: when,
        where: where!.name,
        isPrivate: isPrivate,
        state:
          where.name === "United States of America (USA)"
            ? selectedState
            : null,
      };

      try {
        await fetcher("/api/experiments", requestBody);
        return true;
      } catch (error) {
        console.error("Error in buildRunRequest:", error);
        return false;
      }
    };

    const clearLocalStorage = () => {
      const traitKeys = [
        "age",
        "income",
        "education",
        "gender",
        "children",
        "race",
        "previousSelectedState",
        "ageRange",
        "incomeRange",
        "validationResults",
        "lastValidatedTraits",
        "lastQuestion",
        "dataFetched",
        "baseOption",
        "selectedTrait",
        "populationSizeCount",
        "traitsReset",
        "lastWhere",
      ];
      traitKeys.forEach((key) => localStorage.removeItem(key));
    };

    async function onRunExperiment() {
      if (!canRunExperiments) {
        setShowSubscribeModal(true);
        return;
      }
      if (isPrivate !== null || undefined) {
        let res = await buildRunRequest();
        clearLocalStorage();
        if (res) {
          let runningExperiments =
            JSON.parse(localStorage.getItem("runningExperiments") || "0") + 1;
          localStorage.setItem("runningExperiments", runningExperiments);
          <LogRunExperimentEvent email={user?.email} />;
          router.push("/experiments");
        }
      }
    }

    const handleShowSubscribeModal = () => {
      setShowSubscribeModal(true);
    };
    const ReplayTutorials = () => {
      if (window.pendo && window.pendo.isReady) {
        window.pendo.showGuideById("naq23DrKfGmhtz1HHf6MQpQ5jOg");
      }
    };

    return (
      <ExperimentCreationContext.Provider
        value={{
          question,
          focus,
          activeSpecialist,
          displayTraits,
          populationTraits,
          displayAttributes,
          realWorlBrandAttributeCombinations,
          when,
          where,
          selectedLlmModel,
          selectedState,
          validatedQuestions,
          setValidatedQuestions,
          setSelectedState,
          setQuestion,
          setFocus,
          setActiveSpecialist,
          setPopulationTraits,
          setDisplayTraits,
          setDisplayAttributes,
          setRealWorldBrandAttributeCombinations,
          setWhen,
          setWhere,
          setSelectedLlmModel,
          productExists,
          setProductExists,
          fileState,
          setFileState,
        }}
      >
        {!canRunExperiments && (
          <div className="w-full bg-[#312E81] py-3 flex gap-2 justify-center items-center">
            <p className="text-white font-semibold text-sm">
              Your free trials are complete.
            </p>
            <p className="text-white font-normal text-sm">
              <span
                className=" underline cursor-pointer"
                onClick={handleShowSubscribeModal}
              >
                Upgrade your account
              </span>{" "}
              to keep innovating!{" "}
            </p>
          </div>
        )}

        <SubscribeModal
          showModal={showSubscribeModal}
          setShowModal={setShowSubscribeModal}
        />

        <div className="z-10 py-8 px-10 w-full flex flex-col font-inter h-full">
          <div className="flex justify-between">
            <h1
              id="ideation-why-guide-1"
              className="text-text-dark font-medium text-3xl pb-6"
            >
              Create Experiment
            </h1>
            {/* IDEAS PORTAL BUTTON*/}
            {currStep === 0 && (
              <div style={{ position: "relative", display: "inline-block" }}>
                <div className="flex gap-2">
                  <a
                    href="https://form.typeform.com/to/ViMQd1a2"
                    target="_blank"
                  >
                    {/* <button
                      style={{
                        backgroundColor: isHovered ? "#ECEEF9" : "white",
                      }}
                      className={`group relative flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border`}
                      onMouseEnter={() => setIsHovered(true)}
                      onMouseLeave={() => setIsHovered(false)}
                    >
                      <Sparkle />
                      <div>Request experiment type</div>
                      <Tooltip
                        message="Have a type of experiment you need to run? Contact us"
                        position={"left"}
                      />
                    </button> */}
                  </a>
                  <button
                    onClick={ReplayTutorials}
                    className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
                  >
                    Show Tutorial
                  </button>
                  <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
                    <Header />
                  </div>
                </div>
                {/* {isHovered && (
                  <div
                    style={{
                      visibility: "visible",
                      backgroundColor: "#1C1D47",
                      color: "#fff",
                      textAlign: "left",
                      fontSize: "12px",
                      padding: "7px 8px 8px 8px",
                      borderRadius: "6px",
                      position: "absolute",
                      transform: "translateY(-50%)",
                      top: "39%",
                      right: "103%",
                      opacity: 1,
                      transition: "opacity 0.3s",
                      width: "270px",
                      height: "48px",
                    }}
                  >
                    <p>Have a type of experiment you need to run?</p>
                    <p className="text-left">Contact us!</p>
                  </div>
                )} */}
              </div>
            )}
            {currStep === 2 && (
              // WHO
              <div style={{ position: "relative", display: "inline-block" }}>
                {/* <a href="https://form.typeform.com/to/ViMQd1a2" target="_blank">
                  <button
                    style={{ backgroundColor: isHovered ? "#ECEEF9" : "white" }}
                    className={`group relative flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border`}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                  >
                    <Sparkle />
                    <div>Upload own data</div>
                    <Tooltip
                      message="Are the traits not granular enough? Contact us to upload your own data!"
                      position={"left"}
                    />
                  </button>
                </a> */}
                {/* {isHovered && (
                  <div
                    style={{
                      visibility: "visible",
                      backgroundColor: "#1C1D47",
                      color: "#fff",
                      textAlign: "left",
                      fontSize: "12px",
                      padding: "8px 12px 8px 12px",
                      borderRadius: "6px",
                      position: "absolute",
                      transform: "translateY(-50%)",
                      top: "39%",
                      right: "103%",
                      opacity: 1,
                      transition: "opacity 0.3s",
                      width: "230px",
                      height: "48px",
                    }}
                  >
                    <p>Are the traits not granular enough?</p>
                    <p className="text-left">
                      Contact us to upload your own data!
                    </p>
                  </div>
                )} */}
              </div>
            )}
          </div>
          <div className="pb-4">
            <ProgressIndicator steps={steps} setCurrStep={setCurrStep} />
          </div>
          {/* CONTENT */}
          <div className="flex items-center justify-center pb-4">
            {currStep === 0 && (
              <>
                {/* WHY */}
                <WhyComponent
                  setExperimentPublic={setExperimentPublic}
                  onComplete={completeWhy}
                  setTraits={setTraits}
                  onRunExperiment={onRunExperiment}
                  initialQuestion={question}
                  errorMessage={errorMessage}
                  setErrorMessage={setErrorMessage}
                />
              </>
            )}
            {currStep === 1 && (
              <>
                {/* WHEN/WHERE */}
                <WhenWhereComponent
                  countries={countries}
                  onBack={onBack}
                  onComplete={completeWhenWhere}
                  models={models}
                />
              </>
            )}

            {currStep === 2 && (
              <>
                {/* WHO */}
                <WhoComponent
                  onComplete={completeWho}
                  onBack={onBack}
                  existingYear={null}
                  existingCountry={null}
                  existingQuestion={null}
                  transformedAttributes={transformAttributes}
                  previousStep={previousStep}
                  setPopulationTraits={setPopulationTraits}
                  currentStep={whoSteps}
                  setCurrentStep={setWhoSteps}
                />
              </>
            )}
            {currStep === 3 && (
              <>
                {/* WHAT */}
                <WhatComponent
                  setExperimentPrivate={setExperimentPrivate}
                  setExperimentPublic={setExperimentPublic}
                  transformedAttributes={transformAttributes}
                  onSubmit={onRunExperiment}
                  onBack={onBack}
                />
              </>
            )}
          </div>
        </div>
      </ExperimentCreationContext.Provider>
    );
  },
  { returnTo: "/ideation" }
);
