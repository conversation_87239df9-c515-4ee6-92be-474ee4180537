"use client";
import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { withPageAuthRequired, useUser } from "@auth0/nextjs-auth0/client";
import countries from "../../../../public/data/countries.json";
import ProgressIndicator from "@/app/_components/_ideation/ProgressIndicator";
import WhyComponent from "@/app/_components/_ideation/WhyComponent";
import WhoComponent from "@/app/_components/_ideation/WhoComponent";
import WhatComponent from "@/app/_components/_ideation/WhatComponent";
import {
  CreateExperimentRequest,
  Country,
  DisplayAttribute,
  DisplayTrait,
  Trait,
  LLMModel,
  PopulationTraits,
  BrandAttributeCombination,
  Level,
  FileState,
  TraitCategory,
} from "@/app/_components/_ideation/objects";
import WhenWhereComponent from "@/app/_components/_ideation/WhenWhereComponent";
import ExperimentCreationContext from "@/app/_components/_ideation/ExperimentCreationContext";
import { LogRunExperimentEvent } from "@/app/_components/_util/Analytics";
import * as Sentry from "@sentry/react";

// const fetcher = async (uri: string, body: CreateExperimentRequest) => {
//   try {
//     const response = await fetch(uri, {
//       method: "POST",
//       headers: { "Content-Type": "application/json" },
//       body: JSON.stringify(body),
//     });
//     if (!response.ok) {
//       const error = new Error("Failed to fetch data");
//       Sentry.captureException(error, {
//         extra: { uri, body, status: response.status },
//       });
//       throw error;
//     }
//     const data = await response.json();
//     return data;
//   } catch (error) {
//     Sentry.captureException(error, {
//       extra: { uri, body },
//     });
//     throw error;
//   }
// };

const fetcher = async (uri: string, body: CreateExperimentRequest) => {
  const response = await fetch(uri, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(body),
  });
  const data = await response.json();
  return data;
};

export default withPageAuthRequired(function HumanBaseLineIdeationPage() {
  const router = useRouter();
  const { user } = useUser();

  const pathName = usePathname();
  const id = pathName ? pathName.split("/")[2] : "";

  const [replicateData, setReplicateData] = useState();
  const [isPrivate, setIsPrivate] = useState(false);

  const [steps] = useState([
    { name: "Why", status: "current" },
    { name: "When/where", status: "upcoming" },
    { name: "Who", status: "upcoming" },
    { name: "What", status: "upcoming" },
  ]);

  const [HBReplicateRetryAttributes, setHBReplicateRetryAttributes] = useState(
    []
  );
  const [humanBaselineTraits, setHumanBaselineTraits] = useState([]);

  const [question, setQuestion] = useState("");
  const [focus, setFocus] = useState("engineering");
  const [traits, setTraits] = useState<Record<string, string[]>>({});

  const [activeSpecialist, setActiveSpecialist] = useState("");
  const [displayTraits, setDisplayTraits] = useState<DisplayTrait[]>([]);

  const [populationTraits, setPopulationTraits] = useState<PopulationTraits>({
    state: "New York",
    age: [18, 95],
    household_income: [0, 371000],
    gender: ["Female", "Male"],
    education_level: [
      "High School Diploma",
      "High School but no diploma",
      "Some College",
      "Less than high school",
      "Bachelors",
      "Masters",
      "Associates",
      "PhD",
    ],
    number_of_children: ["0", "1", "2", "4+", "3"],
    racial_group: [
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  });

  const [selectedState, setSelectedState] = useState<string | null>("New York");

  const [existingQuestion, setExistingQuestion] = useState("");
  const [existingYear, setExistingYear] = useState("");
  const [existingCountry, setExistingCountry] = useState("");
  const [productExists, setProductExists] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [fileState, setFileState] = useState<FileState>({
    file: null,
    data: null,
    error: null,
  });

  const [displayAttributes, setDisplayAttributes] = useState<
    DisplayAttribute[]
  >([]);
  const [
    realWorlBrandAttributeCombinations,
    setRealWorldBrandAttributeCombinations,
  ] = useState<BrandAttributeCombination[]>([]);

  const [when, setWhen] = useState("2024");
  const [where, setWhere] = useState<Country>(
    countries.find(
      (country) => country.name === "United States of America (USA)"
    )!
  );

  const [apiString, setApiString] = useState("");
  const models: LLMModel[] = [
    { name: "gpt4" },
    { name: "gpt3" },
    { name: "cohere" },
    { name: "gcp-sonnet" },
    { name: "gcp-gemini" },
  ];
  const [selectedLlmModel, setSelectedLlmModel] = useState<LLMModel>(models[0]);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [previousStep, setPreviousStep] = useState<number>(-1);

  const [validatedQuestions, setValidatedQuestions] = useState<string[]>([]);
  const [whoSteps, setWhoSteps] = useState<"first" | "second" | "third">(
    previousStep !== 3 ? "first" : "third"
  );

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const response = await fetch("/api/token");
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setAccessToken(data.accessToken);
      } catch (error) {
        console.error("Failed to fetch access token:", error);
        Sentry.captureException(error);
      }
    };

    fetchToken();
  }, []);

  const humanBaseLineFetcher = async (uri: string) => {
    try {
      const response = await fetch(uri, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        const error = new Error("Failed to fetch human baseline data");
        Sentry.captureException(error, {
          extra: { uri, status: response.status },
        });
        throw error;
      }
      const data = await response.json();
      return data;
    } catch (error) {
      Sentry.captureException(error, { extra: { uri } });
      throw error;
    }
  };

  useEffect(() => {
    if (id.length < 15) {
      setApiString(
        `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/human-baselines/${id}/replications-amces`
      );
    } else {
      setApiString(`/api/runs/${id}`);
    }
  }, [id]);

  useEffect(() => {
    if (id.length !== 0 && accessToken) {
      if (id.length < 15) {
        humanBaseLineFetcher(apiString)
          .then((res) => {
            const {
              experimentor_why_question_prompt,
              survey_year,
              survey_country,
              why_prompt,
              year,
              country,
              // population_traits,
              pre_cooked_attributes_and_levels_lookup,
            } = res.hb_experiment_definition;

            const whyPrompt = why_prompt ?? experimentor_why_question_prompt;
            const surveyYear = survey_year ?? year;
            const surveyCountry = survey_country ?? country;

            const population_traits = ["age", "education", "gender", "income"];
            const configuredAttrLvls: any = [];
            localStorage.setItem("HBReplicateQuestion", whyPrompt);
            localStorage.setItem("HBReplicateWhere", surveyCountry);

            if (pre_cooked_attributes_and_levels_lookup) {
              for (
                let i = 0;
                i < pre_cooked_attributes_and_levels_lookup.length;
                i++
              ) {
                const attrLevels: any = {};
                const currentAttribute: any =
                  pre_cooked_attributes_and_levels_lookup[i][0];
                const currentLevels =
                  pre_cooked_attributes_and_levels_lookup[i][1];

                attrLevels["attribute"] = currentAttribute;
                attrLevels["levels"] = currentLevels;

                configuredAttrLvls.push(attrLevels);
              }
            }

            const configureLocation: { [key: string]: string } = {
              UK: "United Kingdom",
              "United States": "United States of America (USA)",
              "Montevideo, Uruguay": "Uruguay",
              "Tirania, Albania": "Albania",
            };

            const data: any = {
              causal_question: whyPrompt,
              when: surveyYear,
              // eslint-disable-next-line no-prototype-builtins
              where: configureLocation.hasOwnProperty(surveyCountry)
                ? configureLocation[surveyCountry]
                : surveyCountry,
              characteristics: population_traits.map(
                (trait: string) => trait[0].toUpperCase() + trait.slice(1)
              ),
            };

            const configureWhere: Country | undefined = countries.find(
              (country) => country.name === data.where
            );

            if (configureWhere) {
              setWhere(configureWhere);
            }
            setQuestion(whyPrompt);
            setHBReplicateRetryAttributes(configuredAttrLvls);
            setReplicateData(data);
            setWhen(surveyYear);
          })
          .catch((error) => {
            Sentry.captureException(error, { extra: { id, apiString } });
          });
      } else {
        humanBaseLineFetcher(apiString)
          .then((res) => {
            const {
              experimentor_why_question_prompt,
              survey_year,
              survey_country,
              why_prompt,
              year,
              country,
              pre_cooked_attributes_and_levels_lookup,
              realworld_products,
            } = res.run_details.configs.experiment_design;

            const whyPrompt = why_prompt ?? experimentor_why_question_prompt;
            const surveyYear = survey_year ?? year;
            const surveyCountry = survey_country ?? country;
            if (realworld_products && realworld_products.length) {
              setProductExists(true);
            }

            localStorage.setItem("replicateRetryQuestion", whyPrompt);
            localStorage.setItem("replicateRetryWhere", surveyCountry);
            localStorage.setItem("lastWhere", surveyCountry);
            localStorage.setItem("lastQuestion", whyPrompt);
            const configuredAttrLvls: any = [];
            if (pre_cooked_attributes_and_levels_lookup) {
              for (
                let i = 0;
                i < pre_cooked_attributes_and_levels_lookup.length;
                i++
              ) {
                const attrLevels: any = {};
                const currentAttribute: any =
                  pre_cooked_attributes_and_levels_lookup[i][0];
                const currentLevels =
                  pre_cooked_attributes_and_levels_lookup[i][1];
                attrLevels["attribute"] = currentAttribute;
                attrLevels["levels"] = currentLevels;
                configuredAttrLvls.push(attrLevels);
              }
            }
            const data: any = {
              causal_question: whyPrompt,
              when: surveyYear,
              where: surveyCountry,
            };
            const configureWhere: Country | undefined = countries.find(
              (country) => country.name === data.where
            );
            if (configureWhere) {
              setWhere(configureWhere);
            }
            setQuestion(whyPrompt);
            setHBReplicateRetryAttributes(configuredAttrLvls);
            setReplicateData(data);
            setWhen(surveyYear);

            setExistingQuestion(whyPrompt);
            setExistingYear(surveyYear);
            setExistingCountry(surveyCountry);
          })
          .catch((error) => {
            Sentry.captureException(error, { extra: { id, apiString } });
          });
      }
    }
  }, [id, apiString, accessToken]);

  useEffect(() => {
    setDisplayTraits(transformTraits(traits));
  }, [replicateData]);

  useEffect(() => {
    let transformedAttributes = transformAttributes(HBReplicateRetryAttributes);
    setDisplayAttributes(transformedAttributes);
    localStorage.setItem("attributes", JSON.stringify(transformedAttributes));
  }, [HBReplicateRetryAttributes]);

  const [currStep, setCurrStep] = useState(0);

  function transformTraits(traits: any): DisplayTrait[] {
    const result: DisplayTrait[] = [];

    // Handle each category (usTraits, nonUsTraits, etc.)
    Object.entries(traits).forEach(([category, categoryTraits]) => {
      if (typeof categoryTraits === "object" && categoryTraits !== null) {
        // Handle traits within each category
        Object.entries(categoryTraits as Record<string, string[]>).forEach(
          ([trait, values]) => {
            result.push({
              title: trait.charAt(0).toUpperCase() + trait.slice(1),
              active: false,
              values: Array.isArray(values)
                ? values.map(
                    (item) => item.charAt(0).toUpperCase() + item.slice(1)
                  )
                : [],
              category: category as TraitCategory,
            });
          }
        );
      }
    });

    return result;
  }

  function transformAttributes(attributes: any) {
    let transformedLevels = attributes.map((attr: any, AttrIndex: number) => {
      return attr.levels.map((level: any) => {
        return { level: level, active: AttrIndex < 8 ? true : false };
      });
    });

    return attributes.map((attr: any, idx: any) => {
      return {
        attribute: attr.attribute,
        active: idx < 8 ? true : false,
        levels: transformedLevels[idx],
        attribute_type: attr.attribute_type,
      };
    });
  }

  useEffect(() => {
    if (
      traits &&
      Object.keys(traits).length > 0 &&
      displayTraits.length === 0
    ) {
      try {
        const transformed = transformTraits(traits);
        setDisplayTraits(transformed);
      } catch (error) {
        console.error("Error transforming traits:", error);
      }
    }
  }, [traits, displayTraits.length]);

  const loadPopulationTraitsFromStorage = () => {
    const validationResults = localStorage.getItem("validationResults");
    if (validationResults) {
      const parsed = JSON.parse(validationResults);
      if (parsed.original && parsed.original.population_traits) {
        setPopulationTraits(parsed.original.population_traits);
        return true;
      }
    }
    return false;
  };

  function completeWhy() {
    steps[0].status = "complete";
    if (steps[1].status !== "complete") steps[1].status = "current";
    setPreviousStep(currStep);
    setCurrStep(1);
  }

  function completeWhenWhere() {
    loadPopulationTraitsFromStorage();
    steps[1].status = "complete";
    if (steps[2].status !== "complete") steps[2].status = "current";
    setPreviousStep(currStep);
    setCurrStep(2);
    if (where.name !== "United States of America (USA)") {
      setWhoSteps("third");
    }
  }

  function completeWho() {
    loadPopulationTraitsFromStorage();
    steps[2].status = "complete";
    if (steps[3].status !== "complete") steps[3].status = "current";
    setPreviousStep(currStep);
    setCurrStep(3);
  }

  function onBack() {
    setPreviousStep(currStep); // Track the step we're leaving
    setCurrStep(currStep - 1);
  }

  const clearLocalStorage = () => {
    const traitKeys = [
      "age",
      "income",
      "education",
      "gender",
      "children",
      "race",
      "previousSelectedState",
      "ageRange",
      "incomeRange",
      "validationResults",
      "lastValidatedTraits",
      "lastQuestion",
      "dataFetched",
      "baseOption",
      "selectedTrait",
      "populationSizeCount",
      "traitsReset",
      "lastWhere",
    ];
    traitKeys.forEach((key) => localStorage.removeItem(key));
  };

  const buildRunRequest = async () => {
    if (!question || !when || !where || isPrivate === null || undefined) {
      console.error("One or more fields are missing or invalid.");
      setErrorMessage("One or more fields are missing or invalid.");
      return false;
    }
    localStorage.removeItem("attributes");
    localStorage.removeItem("replicateRetryQuestion");
    localStorage.removeItem("replicateRetryWhere");

    const formattedTraits = displayTraits.map((trait) => ({
      title: trait.title,
      active: trait.active,
      values: trait.values,
    }));

    const requestBody: CreateExperimentRequest = {
      question: question,
      displayTraits: formattedTraits,
      displayAttributes: displayAttributes,
      realworld_products: realWorlBrandAttributeCombinations,
      populationTraits: populationTraits,
      when: when,
      where: where!.name,
      isPrivate: isPrivate,
      state:
        where.name === "United States of America (USA)" ? selectedState : null,
    };

    try {
      await fetcher("/api/experiments", requestBody);
      return true;
    } catch (error) {
      console.error("Error in buildRunRequest:", error);
      return false;
    }
  };

  async function onRunExperiment() {
    let res = await buildRunRequest();
    clearLocalStorage();
    if (res) {
      let runningExperiments =
        JSON.parse(localStorage.getItem("runningExperiments") || "0") + 1;
      localStorage.setItem("runningExperiments", runningExperiments);
      <LogRunExperimentEvent email={user?.email} />;
      router.push("/experiments");
    }
  }

  const setExperimentPrivate = () => {
    setIsPrivate(true);
  };

  const setExperimentPublic = () => {
    setIsPrivate(false);
  };

  return (
    <ExperimentCreationContext.Provider
      value={{
        question,
        focus,
        activeSpecialist,
        displayTraits,
        populationTraits,
        displayAttributes,
        realWorlBrandAttributeCombinations,
        when,
        where,
        selectedLlmModel,
        selectedState,
        validatedQuestions,
        setValidatedQuestions,
        setSelectedLlmModel,
        setQuestion,
        setFocus,
        setActiveSpecialist,
        setDisplayTraits,
        setPopulationTraits,
        setDisplayAttributes,
        setRealWorldBrandAttributeCombinations,
        setWhen,
        setWhere,
        setSelectedState,
        productExists,
        setProductExists,
        fileState,
        setFileState,
      }}
    >
      <div className="z-10 py-8 px-10 w-full flex flex-col font-inter h-full">
        <h1 className="text-text-dark font-medium text-3xl pb-6">
          Create Experiment
        </h1>
        <div className="pb-8">
          <ProgressIndicator steps={steps} setCurrStep={setCurrStep} />
        </div>

        {/* CONTENT */}
        <div className="flex items-center justify-center pb-4">
          {currStep === 0 && (
            <>
              {/* WHY */}
              <WhyComponent
                setExperimentPublic={setExperimentPublic}
                onComplete={completeWhy}
                setTraits={setTraits}
                onRunExperiment={onRunExperiment}
                errorMessage={errorMessage}
                setErrorMessage={setErrorMessage}
              />
            </>
          )}
          {currStep === 1 && (
            <>
              {/* WHEN/WHERE */}
              <WhenWhereComponent
                countries={countries}
                onBack={onBack}
                onComplete={completeWhenWhere}
                models={models}
              />
            </>
          )}
          {currStep === 2 && (
            <>
              {/* WHO */}
              <WhoComponent
                onComplete={completeWho}
                onBack={onBack}
                existingYear={null}
                existingCountry={null}
                existingQuestion={null}
                transformedAttributes={transformAttributes}
                previousStep={previousStep}
                setPopulationTraits={setPopulationTraits}
                currentStep={whoSteps}
                setCurrentStep={setWhoSteps}
              />
            </>
          )}
          {currStep === 3 && (
            <>
              {/* WHAT */}
              <WhatComponent
                onSubmit={onRunExperiment}
                onBack={onBack}
                setExperimentPrivate={setExperimentPrivate}
                transformedAttributes={transformAttributes}
                setExperimentPublic={setExperimentPublic}
              />
            </>
          )}
        </div>
      </div>
    </ExperimentCreationContext.Provider>
  );
});
