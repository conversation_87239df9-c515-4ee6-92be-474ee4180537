"use client";
import "@liveblocks/react-ui/styles.css";
import "@liveblocks/react-tiptap/styles.css";
import "./globals.css";
import "./suppress-hydration.css";

import React, { useEffect } from "react";
import Script from "next/script";
import "./disable-hydration-warnings.js";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { Inter } from "next/font/google";
import { UserProvider } from "@auth0/nextjs-auth0/client";
import ContextWrapper from "./_components/_util/ContextWrappers";
import MainLayout from "../../MainLayout";
import NextTopLoader from "nextjs-toploader";
import { usePathname } from "next/navigation";
import GlobalError from "./_components/global-error";
import MaintenancePage from "./maintenance/page";
import { Auth0TokenProvider } from "./_components/_util/Auth0TokenContext";
import { ApiStatusProvider } from "./_components/_ideation/_who/ApiStatusContext";
import { SubscriptionProvider } from "./_components/_payments/SubscriptionContext";
import { Toaster } from "sonner";
import { Toaster2 } from "@/components/ui/toaster";
import { SerializationMiddleware } from "./_components/_util/SerializationMiddleware";
import GlobalHydrationSuppressor from "@/components/GlobalHydrationSuppressor";
// import LiveblocksWrapper from "./_components/LiveblocksWrapper";

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  useEffect(() => {
    let pageTitle = "Subconscious AI";

    if (pathname !== "/") {
      const formattedPathname = pathname.replace("/", "");
      pageTitle = `${formattedPathname.charAt(0).toUpperCase() + formattedPathname.slice(1)} | Subconscious AI`;
    }

    document.title = pageTitle;
  }, [pathname]);

  return (
    <html lang="en">
      <head>
        <Script id="pendo-installation">
          {`(function(apiKey){
          (function(p,e,n,d,o){var v,w,x,y,z;o=p[d]=p[d]||{};o._q=o._q||[];
          v=['initialize','identify','updateOptions','pageLoad','track'];for(w=0,x=v.length;w<x;++w)(function(m){
            o[m]=o[m]||function(){o._q[m===v[0]?'unshift':'push']([m].concat([].slice.call(arguments,0)));};})(v[w]);
            y=e.createElement(n);y.async=!0;y.src='https://cdn.pendo.io/agent/static/'+apiKey+'/pendo.js';
            z=e.getElementsByTagName(n)[0];z.parentNode.insertBefore(y,z);})(window,document,'script','pendo');
          })('549f5902-27c3-4af4-7620-e455f60dd24c');
          `}
        </Script>
        <Script src="https://www.googletagmanager.com/gtag/js?id=G-52WK8DDZLF" />
        <Script id="google-analytics">
          {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());

              gtag('config', 'G-52WK8DDZLF');
              `}
        </Script>
        <Script id="google-tag-manager" strategy="afterInteractive">
          {`
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-WJGCMSX2');
            `}
        </Script>
      </head>
      <body className={inter.className} cz-shortcut-listen="" suppressHydrationWarning={true}>
        <GlobalHydrationSuppressor />
        <NextTopLoader color="#FFF" />
        <GlobalError>
          <UserProvider>
            <Auth0TokenProvider>
              <SubscriptionProvider>
                <ContextWrapper>
                  <ApiStatusProvider>
                    <QueryClientProvider client={queryClient}>
                      <SerializationMiddleware>
                        <main suppressHydrationWarning={true} className="flex min-h-screen items-center bg-[#1C1D47]">
                          {process.env.NEXT_PUBLIC_MAINTENANCE === "FALSE" ? (
                            <MainLayout>
                              {children}{" "}
                              <Toaster richColors position="top-right" />
                            </MainLayout>
                          ) : (
                            <MaintenancePage />
                          )}
                        </main>
                        <Toaster2 />
                      </SerializationMiddleware>
                    </QueryClientProvider>
                  </ApiStatusProvider>
                </ContextWrapper>
              </SubscriptionProvider>
            </Auth0TokenProvider>
          </UserProvider>
        </GlobalError>
      </body>
    </html>
  );
}
