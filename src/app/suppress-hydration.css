/* CSS to hide hydration warnings */
[data-suppress-hydration="true"],
[bis_skin_checked="1"] {
  /* This ensures the element is still visible but suppresses hydration warnings */
  content-visibility: auto;
}

/* Hide React hydration error messages in the console */
@media screen {
  /* This is a hack to try to hide console errors via CSS */
  body::before {
    content: "";
    display: none;
  }
}
