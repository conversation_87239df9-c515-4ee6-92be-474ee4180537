"use client";
import React from 'react';
import { serializeData } from '@/app/utils/serialization';

/**
 * A middleware component that ensures all data passed to children is serialized
 * This helps prevent "Only plain objects can be passed to Client Components from Server Components" errors
 */
export function SerializationMiddleware({ children }: { children: React.ReactNode }) {
  // This component doesn't actually do any serialization itself
  // It's just a marker to remind developers to use serializeData when passing data
  return <>{children}</>;
}

/**
 * A utility function to safely serialize data before passing it to Client Components
 * @param data Any data that might contain non-serializable objects like Sets
 * @returns Serialized data safe for passing between Server and Client Components
 */
export function withSerialization<T>(data: T): T {
  return serializeData(data) as T;
}
