"use client";
import React, { useCallback, useMemo } from "react";

import SessionContext from "./SessionContext";
import { Run } from "../_experiments/types";
import { Roles } from "../_access/types";
import { serializeData } from "@/app/utils/serialization";

export default function ContextWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const [runs, setRuns] = React.useState<Run[]>([]);
  const [roles, setRoles] = React.useState<Roles>({ roles: [] });
  const [awaitingAccess, setAwaitingAccess] = React.useState<boolean>(true);

  // Wrap the setRuns function to ensure any Set objects are serialized
  // Use useCallback to prevent creating a new function on every render
  const safeSetRuns = useCallback((newRuns: Run[]) => {
    // Only serialize if newRuns is not already serialized
    if (newRuns && typeof newRuns === 'object') {
      setRuns(serializeData(newRuns));
    } else {
      setRuns(newRuns);
    }
  }, []);

  // Wrap the setRoles function to ensure any Set objects are serialized
  // Use useCallback to prevent creating a new function on every render
  const safeSetRoles = useCallback((newRoles: Roles) => {
    // Only serialize if newRoles is not already serialized
    if (newRoles && typeof newRoles === 'object') {
      setRoles(serializeData(newRoles));
    } else {
      setRoles(newRoles);
    }
  }, []);

  // Use useMemo to prevent creating a new context value on every render
  const contextValue = useMemo(() => ({
    runs,
    roles,
    awaitingAccess,
    setRuns: safeSetRuns,
    setRoles: safeSetRoles,
    setAwaitingAccess,
  }), [runs, roles, awaitingAccess, safeSetRuns, safeSetRoles]);

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
}
