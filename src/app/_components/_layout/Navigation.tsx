"use client";
import type { JS<PERSON> } from "react";
import {
  ChevronLeft,
  Settings,
  Home,
  LogOut,
  SquareX,
  Users,
  MessageSquarePlus,
  LayoutGrid,
  BarChart2,
  <PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useUser } from "@auth0/nextjs-auth0/client";
import { TagUser } from "../_util/Analytics";
import React, { Fragment, useContext, useEffect, useState } from "react";
// import { HydrationSuppressor } from "@/components/HydrationSuppressor";

import SessionContext from "../_util/SessionContext";
import { usePathname } from "next/navigation";

import * as Sentry from "@sentry/react";
import { Dialog, Transition } from "@headlessui/react";

function formatName(name: string | null | undefined, email: string) {
  let displayName = name;
  if (name === null || name === undefined) {
    displayName = email;
  } else {
    // If first and last name is too long, show only first name
    if (displayName!.length > 10) {
      displayName = displayName!.split(" ")[0];
    }
  }

  if (displayName!.length > 10) {
    displayName = displayName!.substring(0, 10) + "...";
  }

  return displayName;
}

const fetchRoles = async () => {
  const startTime = Date.now();
  try {
    const response = await fetch("/api/util/roles");

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Log error with contextual information
    Sentry.captureException(error, {
      tags: {
        api_endpoint: "/api/util/roles",
        status: "error",
      },
      extra: {
        response_time: `${duration}ms`,
        response_status: (error as any).response
          ? (error as any).response.status
          : "unknown",
        error_message: (error as any).message,
      },
    });

    throw error;
  }
};

const navigationTabs = [
  {
    title: "Home",
    icon: <Home />,
    tabLink: "/",
  },
  {
    title: "Create Experiment",
    icon: <Hammer />,
    tabLink: "/ideation",
  },
  {
    title: "Experiment Results",
    icon: <BarChart2 />,
    tabLink: "/experiments",
  },
  {
    title: "Showcase",
    icon: <LayoutGrid />,
    tabLink: "/showcase",
  },
  {
    title: "Request a Feature",
    icon: <MessageSquarePlus />,
    tabLink: "https://github.com/Subconscious-ai/sublime/discussions",
  },
  {
    title: "Collaboratory",
    icon: <Users />,
    tabLink: "/collaboratory",
  },
];

const Navigation = () => {
  const pathname = usePathname();
  const { user, isLoading } = useUser();
  const { setAwaitingAccess, setRoles } = useContext(SessionContext);
  const [activeComponent, setActiveComponent] = useState("/");
  const [isExpanded, setIsExpanded] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [buttonText, setButtonText] = useState("Log out");
  const [filteredNavTabs, setFilteredNavTabs] = useState(navigationTabs);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;
      const isRestrictedDomain =
        hostname === "app.test.subconscious.ai" ||
        hostname === "app.subconscious.ai";

      // Filter out Collaboratory tab for restricted domains
      const tabs = isRestrictedDomain
        ? navigationTabs.filter((tab) => tab.title !== "Collaboratory")
        : navigationTabs;

      setFilteredNavTabs(tabs);
    }
  }, []);

  const handleLogout = () => {
    setButtonText("Logging out...");
    window.location.href = "/api/auth/logout";
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };
  const toggleWidth = () => {
    setIsExpanded(!isExpanded);
  };

  useEffect(() => {
    if (typeof window !== "undefined" && pathname) {
      setActiveComponent(pathname);
    }
  }, [pathname]);

  useEffect(() => {
    const handleRouteChange = () => {
      if (typeof window !== "undefined" && pathname) {
        setActiveComponent(pathname);
      }
    };

    if (typeof window !== "undefined") {
      window.onpopstate = handleRouteChange;
    }

    return () => {
      if (typeof window !== "undefined") {
        window.onpopstate = null;
      }
    };
  }, [pathname]);

  useEffect(() => {
    if (user !== undefined) {
      // @ts-ignore
      // eslint-disable-next-line no-undef
      pendo.initialize({
        visitor: {
          id: user?.sub,
          email: user?.email,
          roles: user?.["https://www.api.subconscious.ai/roles"],
          name: user?.name,
          picture: user?.picture,
        },
      });
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      fetchRoles()
        .then((roles) => {
          setRoles(roles);
          if (
            roles === undefined ||
            roles.roles.length === 0 ||
            !roles.roles.find(
              (r: string) =>
                r.toLowerCase() === "employee" || r.toLowerCase() === "customer"
            )
          ) {
            setAwaitingAccess(true);
          } else {
            setAwaitingAccess(false);
          }
        })
        .catch((e) => {
          Sentry.captureException(e);
        });
    }
  }, [user, setRoles, setAwaitingAccess]);

  const handleClick = (href: string) => {
    setActiveComponent(href);
  };

  return (
    <div
      suppressHydrationWarning
      className={`relative flex flex-col h-screen justify-between text-white font-inter font-normal text-base transition-all duration-500 ${
        isExpanded ? "w-64" : "w-[83px]"
      }`}
    >
      {/* NAV */}
      <div suppressHydrationWarning className="flex flex-col flex-start gap-6 pt-8 justify-center">
        {/* HEADER */}
        <a href="https://subconscious.ai">
          <div suppressHydrationWarning className="flex flex-row items-center gap-2 px-6">
            <Image
              src={"/brain_logo.png"}
              width={35}
              height={32}
              alt="Subconscious.ai logo"
            />
            <span
              className={`transition-opacity text-sm duration-500 overflow-hidden ${isExpanded ? "w-56 opacity-100" : "w-20 opacity-0 pointer-events-none"}`}
            >
              subconscious.ai
            </span>
            <span
              className={`bg-[#3935B5] py-1 rounded-full flex items-center justify-center font-normal text-xs px-4 transition-opacity duration-500 overflow-hidden ${isExpanded ? "opacity-100" : "opacity-0 pointer-events-none"}`}
            >
              BETA
            </span>
          </div>
        </a>
        <button
          onClick={toggleWidth}
          className="absolute top-11 transform -translate-y-1/2 translate-x-1/2 right-3 gap-2 p-[6px] z-10 border border-[#667085] rounded-lg -mr-3 w-7 h-7 bg-[#1C1D47] cursor-pointer flex items-center justify-center"
        >
          <ChevronLeft
            className={`w-4 h-4 transition-transform duration-500 ${
              isExpanded ? "" : "rotate-180"
            }`}
          />
        </button>

        {/* NAVIGATION */}
        <div
          suppressHydrationWarning
          className={`flex flex-col gap-2 px-4 transition-all duration-500 ${isExpanded ? "w-64" : "w-20"}`}
        >
          {filteredNavTabs.map((tab, idx) => {
            const { title, icon, tabLink } = tab as {
              title: string;
              icon: JSX.Element;
              tabLink: string;
            };
            return (
              <Link href={tabLink} key={idx}>
                <div
                  suppressHydrationWarning
                  className={`group relative flex items-center gap-3 rounded-md p-3 hover:bg-[#2D2E61] ${
                    activeComponent === tabLink ? "bg-[#2D2E61]" : ""
                  }`}
                  onMouseDown={() => handleClick(tabLink)}
                >
                  <div suppressHydrationWarning className="relative flex items-center">
                    <span className="w-6 h-6">{icon}</span>
                    {!isExpanded && (
                      <span className="absolute left-full ml-5 min-w-max max-w-xs w-[68px] h-[32px] px-3 py-2 bg-[#2D2E61] text-white text-xs rounded-md z-20 font-roboto font-semibold text-center opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                        {title}
                      </span>
                    )}
                  </div>
                  <p
                    className={`transition-opacity  duration-500 whitespace-nowrap overflow-hidden ${isExpanded ? "opacity-100" : "opacity-0 pointer-events-none"}`}
                  >
                    {title}
                  </p>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
      {/* FOOTER */}
      <div
        suppressHydrationWarning
        className={` flex flex-col px-4 pb-6 divide-y divide-white gap-2 justify-center ${isExpanded ? "w-64" : "w-20"}`}
      >
        <Link href={"/settings"}>
          <div
            suppressHydrationWarning
            className={`group relative flex flex-row gap-3 rounded-md p-3 hover:bg-[#2D2E61] ${
              activeComponent === "/settings" ? "bg-[#2D2E61]" : ""
            }`}
            onMouseDown={() => handleClick("/settings")}
          >
            <div suppressHydrationWarning={true} className="relative flex items-center">
              <span className="w-6 h-6">
                <Settings />
              </span>
              {!isExpanded && (
                <span className="absolute left-full ml-5 min-w-max max-w-xs w-[68px] h-[32px] px-3 py-2 bg-[#2D2E61] text-white text-xs rounded-md z-20 font-roboto font-semibold text-center opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                  Settings
                </span>
              )}
            </div>

            <p
              className={`transition-opacity duration-500 overflow-hidden ${isExpanded ? "opacity-100" : "opacity-0 pointer-events-none"}`}
            >
              Settings
            </p>
          </div>
        </Link>
        {user && (
          <>
            <TagUser email={user.email} />
            <div
              suppressHydrationWarning
              className={`flex flex-row items-center gap-3 pt-6 mb-3 transition-all duration-500 ${
                isExpanded ? "justify-between" : ""
              }`}
            >
              <div
                suppressHydrationWarning
                className={`flex items-center transition-all duration-500 overflow-hidden h-10 ${
                  isExpanded ? "w-[150px] opacity-100" : "w-0 opacity-0"
                }`}
              >
                <Image
                  className="rounded-full transition-all duration-500"
                  src={user!.picture!}
                  height={isExpanded ? 40 : 0}
                  width={isExpanded ? 40 : 0}
                  alt="User Profile Picture"
                />
                <p className="font-semibold transition-opacity duration-500 ml-3 whitespace-nowrap overflow-hidden text-ellipsis">
                  {formatName(user!.name, user!.email!)}
                </p>
              </div>
              <button
                className="absolute right-8 cursor-pointer transition-all duration-500"
                onClick={openModal}
              >
                <LogOut />
              </button>
            </div>
            <Transition.Root show={isModalOpen} as={Fragment}>
              <Dialog as="div" className="relative z-50" onClose={closeModal}>
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="fixed inset-0 bg-[#00000099] bg-opacity-75 transition-opacity" />
                </Transition.Child>

                <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                  <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <Transition.Child
                      as={Fragment}
                      enter="ease-out duration-300"
                      enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                      enterTo="opacity-100 translate-y-0 sm:scale-100"
                      leave="ease-in duration-200"
                      leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                      leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                    >
                      <Dialog.Panel className="relative transform w-[452px] overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all sm:my-8 sm:p-6">
                        <div className="sm:flex sm:items-start">
                          <div className="mt-3 flex flex-col text-center sm:mt-0 sm:text-left">
                            <div className="flex justify-between items-center mb-4 gap-5">
                              <h2 className="text-xl font-roboto font-semibold text-[#101828]">
                                Are you sure you want to log out ?
                              </h2>
                              <button
                                onClick={closeModal}
                                className="text-gray-600 hover:text-gray-800"
                              >
                                <SquareX />
                              </button>
                            </div>
                            <div className="flex justify-between gap-5 mt-8">
                              <button
                                onClick={closeModal}
                                className="bg-white text-gray-800 font-normal px-3 py-2 gap-2 font-roboto shadow-xs shadow-0 text-base rounded-lg w-48 h-11 border-[#D0D5DD] border"
                              >
                                Cancel
                              </button>
                              <button
                                onClick={handleLogout}
                                disabled={isLoading}
                                className="bg-[#312E81] text-white font-semibold px-3 py-2 gap-2 font-roboto text-base rounded-lg w-48 h-11"
                              >
                                {buttonText}
                              </button>
                            </div>
                          </div>
                        </div>
                      </Dialog.Panel>
                    </Transition.Child>
                  </div>
                </div>
              </Dialog>
            </Transition.Root>
          </>
        )}
      </div>
    </div>
  );
};

export default Navigation;
