/* eslint-disable no-unused-vars */

"use client";
import React, { useContext, useState, useEffect } from "react";
import { BoltIcon } from "@heroicons/react/24/outline";
import SuggestionQuestions from "./_why/SuggestionQuestions";
import { PlaceholdersAndVanishInput } from "../_ui/placeholders-and-vanish-input";
// import Tooltip from "../_insights/Tooltip";
import Tooltip from "../../_components/_util/ToolTip";
import { usePathname } from "next/navigation";
import { useUser } from "@auth0/nextjs-auth0/client";
import { Run } from "../_experiments/types";
import SessionContext from "../../_components/_util/SessionContext";
import traitValues from "./traits";

import { Attribute, Trait } from "./objects";
import ExperimentCreationContext from "./ExperimentCreationContext";
import { Lightbulb, TriangleAlert, Zap } from "lucide-react";
import CausalCarouselModal from "./_why/CausalCarouselModal";
import SubscribeModal from "../_payments/SubscribeModal";

import * as Sentry from "@sentry/react";
import { ErrorMessage } from "@/app/utils/errorMessage";
import { useAuth0Token } from "../_util/Auth0TokenContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  useIdeationTracking,
  useButtonTracking,
  useFormTracking,
} from "@/hooks/usePendoTracking";

interface WhyComponentProps {
  onComplete: () => void;
  setTraits: React.Dispatch<React.SetStateAction<Record<string, string[]>>>;
  onRunExperiment: () => void;
  setExperimentPublic: () => void;
  initialQuestion?: string;
  errorMessage: string | null;
  setErrorMessage: React.Dispatch<React.SetStateAction<string | null>>;
}

const WhyComponent = ({
  setExperimentPublic,
  onComplete,
  setTraits,
  onRunExperiment,
  initialQuestion,
  errorMessage,
  setErrorMessage,
}: WhyComponentProps) => {
  const {
    question,
    focus,
    validatedQuestions,
    setQuestion,
    setFocus,
    setDisplayAttributes,
    setValidatedQuestions,
  } = useContext(ExperimentCreationContext);
  const pathname = usePathname();
  const isReplicateExperiment = !!pathname?.match(/^\/ideation\/[^/]+$/);

  const { user } = useUser();
  const { auth0AccessToken } = useAuth0Token();

  const [showSuggestionQuestions, setShowSuggestionQuestions] = useState(false);
  // const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showCausalCarouselModal, setShowCausalCarouselModal] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState("");
  const [focusCard, setFocusCard] = useState("");
  const [isLuckyLoading, setIsLuckyLoading] = useState(false);

  const [showSubscribeModal, setShowSubscribeModal] = useState<boolean>(false);

  const [stripeCustomerId, setStripeCustomerId] = useState<string | null>(null);
  const [renewalDate, setRenewalDate] = useState<string | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<string | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);
  const [hasShownSuggestions, setHasShownSuggestions] = useState(false);

  const [roles, setRoles] = useState<string[]>([]);
  const [userRunsCount, setUserRunsCount] = useState<number>(0);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  // Pendo tracking hooks
  const {
    trackQuestionAsked: trackQuestionAskedPendo,
    trackStepStart,
    trackStepComplete,
  } = useIdeationTracking();
  const trackFeelingLuckyClick = useButtonTracking(
    "feeling_lucky_button",
    "ideation_flow"
  );
  const trackContinueClick = useButtonTracking(
    "continue_button",
    "ideation_flow"
  );
  const { onFieldFocus, onFieldBlur } = useFormTracking("why_question_form");
  const [accessToken, setAccessToken] = useState<string | null>(null);

  const handleSuggestionClick = (selectedQuestion: string) => {
    setQuestion(selectedQuestion);
    setValidatedQuestions([...validatedQuestions, selectedQuestion]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuestion(e.target.value);
  };

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const response = await fetch("/api/token");
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setAccessToken(data.accessToken);
      } catch (error) {
        console.error("Failed to fetch access token:", error);
        Sentry.captureException(error);
      }
    };

    fetchToken();
  }, []);

  useEffect(() => {
    if (initialQuestion) {
      setQuestion(initialQuestion);
    }
  }, [initialQuestion, setQuestion]);

  useEffect(() => {
    if (user && auth0AccessToken) {
      fetch(
        `${process.env.NEXT_PUBLIC_AUTH0_AUDIENCE}users?q=user_id:"${user.sub}"&search_engine=v3`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${auth0AccessToken}`,
          },
        }
      )
        .then((response) => response.json())
        .then((data) => {
          const userData = data[0];
          const { app_metadata } = userData;

          if (app_metadata) {
            if (app_metadata.stripe_customer_id) {
              setStripeCustomerId(app_metadata.stripe_customer_id);
            }
            if (app_metadata.roles) {
              setRoles(app_metadata.roles); // Set roles
            }
          }
        })
        .catch((error) => {
          console.error("Error fetching user data:", error);
        });
    }
  }, [user, auth0AccessToken]);

  const checkSubscriptionStatus = async () => {
    const startTime = Date.now();
    try {
      const token = accessToken;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/check-subscription/?customer_id=${stripeCustomerId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const duration = Date.now() - startTime;

      if (!response.ok) {
        const errorMessage = await response.text();
        console.error("Error checking subscription:", errorMessage);
        setError(errorMessage);
        return;
      }

      const data = await response.json();
      setSubscriptionStatus(
        data.subscription_status.status === "active" ||
          data.subscription_status.status === "trialing"
          ? "Active"
          : "Not Subscribed"
      );

      const renewalDateInEpoch = data.subscription_status.current_period_end;
      const renewalDate = new Date(renewalDateInEpoch * 1000);

      const formattedRenewalDate = renewalDateInEpoch
        ? renewalDate.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })
        : null;
      setRenewalDate(formattedRenewalDate);
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log error
      Sentry.captureException(error, {
        tags: {
          api_endpoint: `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/check-subscription`,
          user_id: user?.sub,
          status: "error",
        },
        extra: {
          response_time: `${duration}ms`,
          error_message: (error as any).message,
        },
      });

      setError("An error occurred while checking subscription status.");
    }
  };

  useEffect(() => {
    if (stripeCustomerId) {
      checkSubscriptionStatus();
    }
  }, [stripeCustomerId]);

  const showModal = () => {
    setShowSubscribeModal(true);
  };

  const placeholders = [
    "How does US Immigration Policy affect individuals' decisions to immigrate?",
    "What factors influence attitudes towards teleworking colleagues?",
    "What causes individuals to buy a generative AI product?",
    "What causes a person to want to drive a car?",
  ];

  useEffect(() => {
    // Ensure initial value is set
    if (question) {
      setQuestion(question);
    }
  }, [question, setQuestion]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuestion(e.target.value);
  };

  const onSubmit = (
    e:
      | React.FormEvent<HTMLFormElement>
      | React.KeyboardEvent<HTMLTextAreaElement>
  ) => {
    e.preventDefault();
    onContinue();
  };

  const checkCausality = async (why_prompt: string, focusCard: string) => {
    setDisplayAttributes([]); //To not mislead other why_prompt attributes to run new why_prompt using I'm lucky feature
    const startTime = Date.now();
    try {
      const response = await fetch("/api/copilot/check-causality", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ why_prompt }),
      });

      if (!response.ok) {
        throw new Error(ErrorMessage.causalityCheck);
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      const duration = Date.now() - startTime;

      // Log error
      Sentry.captureException(error, {
        tags: {
          api_endpoint: "/api/copilot/check-causality",
          user_id: user?.sub,
          status: "error",
        },
        extra: { response_time: `${duration}ms`, error_message: error.message },
      });

      return { error: error.message };
    }
  };

  const fetchTraits = (): {} => {
    try {
      return traitValues;
    } catch (error: any) {
      Sentry.captureException(error, {
        tags: { source: "local_data", user_id: user?.sub, status: "error" },
        extra: { error_message: error.message },
      });
      return [];
    }
  };

  const checkUserRunsCount = async () => {
    const startTime = Date.now();
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/user-runs-count`,
        { method: "GET", headers: { Authorization: `Bearer ${accessToken}` } }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch user runs count");
      }

      const data = await response.json();
      setUserRunsCount(parseInt(data.runs_count));
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log error
      Sentry.captureException(error, {
        tags: {
          api_endpoint: `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/user-runs-count`,
          user_id: user?.sub,
          status: "error",
        },
        extra: {
          response_time: `${duration}ms`,
          error_message: (error as any).message,
        },
      });

      console.error("Error fetching user runs count:", error);
    }
  };

  useEffect(() => {
    if (user && accessToken) {
      checkUserRunsCount();
    }
  }, [user, accessToken]);

  // Add this state at the top with other states

  const onContinue = async () => {
    setErrorMessage(null);

    // Skip causality checks if it's an replicate or suggested question
    if (
      isReplicateExperiment ||
      validatedQuestions.includes(question) ||
      hasShownSuggestions
    ) {
      // Skip directly to fetching traits
      setLoadingMessage("Fetching relevant traits...");
      const traitsResult = fetchTraits();
      setTraits(Object.keys(traitsResult).length > 0 ? traitsResult : []);
      onComplete();
      return;
    }

    if (question !== "" && !isLoading) {
      try {
        setIsLoading(true);
        setLoadingMessage(
          "We're reviewing the content for moderation... This won't take long."
        );

        // Step 1: Causality check
        const causalityCheck = await checkCausality(question, focusCard);

        if (causalityCheck.error) {
          setSuggestions(shuffledQuestions.slice(0, 3));
          setShowSuggestionQuestions(true);
          setErrorMessage(ErrorMessage.causalityCheck);
          setHasShownSuggestions(true);
          return; // Stop here on first attempt
        }

        // Always show suggestions if they exist
        if (
          causalityCheck.suggestions &&
          causalityCheck.suggestions.length > 0
        ) {
          setSuggestions(causalityCheck.suggestions);
          setShowSuggestionQuestions(true);
        } else {
          setSuggestions(shuffledQuestions.slice(0, 3));
          setShowSuggestionQuestions(true);
        }

        if (causalityCheck.is_causal) {
          // Step 2: Fetch traits
          setValidatedQuestions([...validatedQuestions, question]);
          setLoadingMessage("Fetching relevant traits...");
          const traitsResult = fetchTraits();
          setTraits(Object.keys(traitsResult).length > 0 ? traitsResult : []);
          setErrorMessage("Ideas similar to original prompt");
        } else {
          setErrorMessage(ErrorMessage.causalityCheck);
          setHasShownSuggestions(true);
        }
      } catch (error) {
        console.error("Error in onContinue:", error);
        setErrorMessage("An unexpected error occurred. Please try again.");
        setSuggestions(shuffledQuestions.slice(0, 3));
        setShowSuggestionQuestions(true);
        setHasShownSuggestions(true);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Add this effect to reset the flag when question changes
  useEffect(() => {
    setHasShownSuggestions(false);
  }, [question]);

  // Make sure FALLBACK_QUESTIONS has some default questions

  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000;

  const FALLBACK_QUESTIONS: string[] = [
    "What factors influence a person's decision to buy a house?",
    "What drives sustainable product purchases?",
    "What influences recycling behavior?",
    "What influences AI tool adoption?",
    "What factors affect technology learning curves?",
    "What drives cloud service adoption?",
    "What causes people to adopt green technologies?",
    "What social media platform features influence teenage mental health?",
    "What causes individuals to adopt eco-friendly behaviors?",
    "What influences social media engagement?",
    "What causes online community participation?",
    "What factors affect digital communication preferences?",
  ];

  const shuffledQuestions = [...FALLBACK_QUESTIONS].sort(
    () => 0.5 - Math.random()
  );

  class TimeoutError extends Error {
    constructor(message: string) {
      super(message);
      this.name = "TimeoutError";
    }
  }

  function isTimeoutError(error: unknown): error is TimeoutError {
    return (
      error instanceof TimeoutError &&
      error.message === "Timeout after 6 seconds"
    );
  }

  function isError(error: unknown): error is Error {
    return error instanceof Error;
  }

  const checkCausalityWithTimeout = async (
    why_prompt: string,
    focusCard: string
  ) => {
    const timeout = new Promise((_, reject) =>
      setTimeout(
        () => reject(new TimeoutError("Timeout after 60 seconds")),
        60000
      )
    );

    const causalityPromise = checkCausality(why_prompt, focusCard);

    try {
      return await Promise.race([causalityPromise, timeout]);
    } catch (error) {
      if (error instanceof TimeoutError) {
        throw error;
      }
      throw error;
    }
  };
  const retryCheckCausality = async (
    why_prompt: string,
    focusCard: string
  ): Promise<any> => {
    for (let i = 0; i < MAX_RETRIES; i++) {
      try {
        const result = await checkCausalityWithTimeout(why_prompt, focusCard);
        setRetryCount(0);
        setIsRetrying(false);
        return result;
      } catch (error) {
        if (i < MAX_RETRIES - 1) {
          setRetryCount(i + 1);
          setIsRetrying(true);
          await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
        } else {
          setIsRetrying(false);
          throw error;
        }
      }
    }
  };

  async function onBrainstorm() {
    setErrorMessage(null);
    setIsLoading(true);
    setShowSuggestionQuestions(true);
    setLoadingMessage(
      "Crafting unique concepts tailored to you... Great ideas are just seconds away."
    );

    try {
      if (question && question.trim() !== "") {
        // If there's a question, use it to generate related suggestions
        const causalityCheck = await retryCheckCausality(question, focusCard);

        if (
          causalityCheck.suggestions &&
          causalityCheck.suggestions.length > 0
        ) {
          // Take only first 3 suggestions
          setSuggestions(causalityCheck.suggestions.slice(0, 3));
          setValidatedQuestions([
            ...validatedQuestions,
            ...causalityCheck.suggestions.slice(0, 3),
          ]);
          setErrorMessage("");
        } else {
          // If no suggestions from API, show 3 random fallback questions
          setSuggestions(FALLBACK_QUESTIONS.slice(0, 3));
          setValidatedQuestions([
            ...validatedQuestions,
            ...FALLBACK_QUESTIONS.slice(0, 3),
          ]);
          setErrorMessage("");
        }
      } else {
        // If no question, generate exactly 3 questions from themes
        const themes = [
          {
            domain: "mobile",
            questions: [
              "What factors influence smartphone brand loyalty?",
              "What causes people to upgrade their mobile devices?",
              "How do mobile app features affect user engagement?",
            ],
          },
          {
            domain: "workplace",
            questions: [
              "What influences employee job satisfaction?",
              "What factors drive workplace productivity?",
              "What causes employees to change jobs?",
            ],
          },
          {
            domain: "technology",
            questions: [
              "What influences AI tool adoption?",
              "What factors affect technology learning curves?",
              "What drives cloud service adoption?",
            ],
          },
          {
            domain: "social",
            questions: [
              "What influences social media engagement?",
              "What causes online community participation?",
              "What factors affect digital communication preferences?",
            ],
          },
          {
            domain: "environment",
            questions: [
              "What drives sustainable product purchases?",
              "What influences recycling behavior?",
              "What causes people to adopt green technologies?",
            ],
          },
        ];

        // Select 3 random themes
        const selectedThemes = themes
          .sort(() => 0.5 - Math.random())
          .slice(0, 3);

        // Get one random question from each selected theme
        const generatedQuestions = selectedThemes.map((theme) => {
          const randomIndex = Math.floor(
            Math.random() * theme.questions.length
          );
          return theme.questions[randomIndex];
        });

        setSuggestions(generatedQuestions);
        setValidatedQuestions([...validatedQuestions, ...generatedQuestions]);
        setErrorMessage("");
      }
    } catch (error) {
      console.error("Error in brainstorm:", error);
      // Show exactly 3 fallback questions in case of error
      setSuggestions(FALLBACK_QUESTIONS.slice(0, 3));
      setErrorMessage(
        "Sorry we couldn't quite get that. Here are some suggested questions to help you get started:"
      );
      Sentry.captureException(error);
    } finally {
      setIsLoading(false);
      setRetryCount(0);
      setIsRetrying(false);
    }
  }

  useEffect(() => {
    if (isRetrying) {
      setLoadingMessage(`Retrying... Attempt ${retryCount} of ${MAX_RETRIES}`);
    }
  }, [isRetrying, retryCount]);

  function trackQuestionAsked() {
    // Use the new centralized tracking
    trackQuestionAskedPendo(question, {
      question_length: question.length,
      has_shown_suggestions: hasShownSuggestions,
      retry_count: retryCount,
      user_runs_count: userRunsCount,
    });
  }

  const onFeelingLucky = async () => {
    setErrorMessage(null);

    // Track feeling lucky button click
    trackFeelingLuckyClick({
      question_text: question,
      question_length: question.length,
      user_runs_count: userRunsCount,
    });

    if (question !== "") {
      try {
        setIsLuckyLoading(true);

        const canRunExperiment =
          userRunsCount < 2 ||
          subscriptionStatus === "Active" ||
          roles.includes("employee") ||
          roles.includes("customer");

        if (!canRunExperiment) {
          setShowSubscribeModal(true);
          return;
        }

        // Only AFTER checking if the user can run experiments, then check if we can skip validation
        if (
          isReplicateExperiment ||
          validatedQuestions.includes(question) ||
          hasShownSuggestions
        ) {
          // Skip directly to running the experiment for eligible users
          await trackQuestionAsked();
          setExperimentPublic();
          await onRunExperiment();
          return;
        }

        // Using the same loading message as onContinue for consistency
        setLoadingMessage(
          "We're reviewing the content for moderation... This won't take long."
        );

        // Step 1: Causality check (similar to onContinue)
        const causalityCheck = await checkCausality(question, focusCard);

        if (causalityCheck.error) {
          // Match exactly how onContinue handles this case
          setSuggestions(shuffledQuestions.slice(0, 3));
          setShowSuggestionQuestions(true);
          setErrorMessage(ErrorMessage.causalityCheck);
          setHasShownSuggestions(true);
          return;
        }

        if (causalityCheck.is_causal) {
          // If causal, proceed with experiment
          setValidatedQuestions([...validatedQuestions, question]);
          await trackQuestionAsked();
          setExperimentPublic();
          await onRunExperiment();
        } else {
          // If not causal, show suggestions exactly like onContinue does
          if (
            causalityCheck.suggestions &&
            causalityCheck.suggestions.length > 0
          ) {
            setSuggestions(causalityCheck.suggestions);
          } else {
            setSuggestions(shuffledQuestions.slice(0, 3));
          }
          setShowSuggestionQuestions(true);
          setErrorMessage(ErrorMessage.causalityCheck);
          setHasShownSuggestions(true);
        }
      } catch (error) {
        console.error("Error in feeling lucky:", error);
        // Match exactly how onContinue handles errors
        setErrorMessage("An unexpected error occurred. Please try again.");
        setSuggestions(shuffledQuestions.slice(0, 3));
        setShowSuggestionQuestions(true);
        setHasShownSuggestions(true);
        Sentry.captureException(error);
      } finally {
        setIsLuckyLoading(false);
      }
    }
  };

  return (
    <div className="flex flex-col gap-6 items-start max-w-4xl w-[800px]">
      <div className="flex flex-col gap-3 w-full mt-12">
        <div className="flex max-xl:flex-col justify-between items-center gap-3 w-full">
          <h2
            id="ideation-why-guide-2"
            className="max-xl:flex max-xl:justify-start max-xl:w-full text-text-dark font-roboto font-medium text-2xl"
          >
            Which decision scenario would you like to investigate?
          </h2>
          <div className="max-xl:flex max-xl:justify-end max-xl:w-full items-center">
            <button
              id="ideation-why-guide-4"
              className="text-primary font-roboto font-medium text-md flex space-x-1 items-center hover:bg-secondary-grey"
              onMouseDown={() => {
                setIsLoading(true);
                onBrainstorm();
              }}
            >
              <div className="justify-center items-center">
                <Lightbulb size={20} />
              </div>
              Brainstorm questions
            </button>
          </div>
        </div>
        <div style={{ position: "relative" }}>
          {isLoading && (
            <div
              className={`flex flex-col gap-0 py-4 items-center justify-center w-full h-full absolute inset-0`}
            >
              <div className="flex space-x-2 justify-center items-center">
                <span className="sr-only">Loading...</span>
                <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="h-4 w-4 bg-primary rounded-full animate-bounce"></div>
              </div>
              <p className="text-sm text-primary">{loadingMessage}</p>
            </div>
          )}
        </div>
        <div className={loadingMessage ? "w-full pt-8" : "w-full"}>
          <PlaceholdersAndVanishInput
            placeholders={placeholders}
            onChange={handleChange}
            onSubmit={onSubmit}
            initialValue={question}
            setErrorMessage={setErrorMessage}
          />
        </div>
        {errorMessage && (
          <>
            {errorMessage.includes("Ideas similar to original prompt") ? (
              <Alert className="bg-blue-50 border-blue-200 flex items-center gap-2">
                <div className="flex items-center">
                  <Lightbulb className="h-6 w-6 text-blue-500 flex-shrink-0" />
                </div>
                <div className="flex items-center">
                  <AlertDescription className="text-sm text-blue-900">
                    {errorMessage}
                  </AlertDescription>
                </div>
              </Alert>
            ) : errorMessage.includes("Initial suggestions below") ? (
              <Alert className="flex items-center gap-2">
                <div className="flex items-center">
                  <Lightbulb className="h-6 w-6 text-yellow-500 flex-shrink-0" />
                </div>
                <div className="flex items-center">
                  <AlertDescription className="text-sm text-yellow-900">
                    {errorMessage}
                  </AlertDescription>
                </div>
              </Alert>
            ) : (
              <Alert variant="destructive" className="flex items-center gap-2">
                <div className="flex items-center">
                  <TriangleAlert className="h-6 w-6 text-red-500 flex-shrink-0" />
                </div>
                <div className="flex items-center">
                  <AlertDescription className="text-sm text-red-900">
                    {errorMessage.includes(
                      "This prompt is not suitable for analysis using conjoint methods, which are designed to understand individual preferences and trade-offs. View tutorial or use one of the suggestions below:"
                    ) ? (
                      <p>
                        This prompt is not suitable for analysis using conjoint
                        methods, which are designed to understand individual
                        preferences and trade-offs. Move forward with the same
                        prompt or{" "}
                        <span
                          className="font-medium cursor-pointer underline"
                          onClick={() => setShowCausalCarouselModal(true)}
                        >
                          View tutorial
                        </span>{" "}
                        or use one of the suggestions below.
                      </p>
                    ) : (
                      <span
                        dangerouslySetInnerHTML={{ __html: errorMessage }}
                      />
                    )}
                  </AlertDescription>
                </div>
              </Alert>
            )}
          </>
        )}
        {showSuggestionQuestions && (
          <SuggestionQuestions
            suggestion={suggestions}
            onSuggestionClick={handleSuggestionClick}
          />
        )}
        <CausalCarouselModal
          showModal={showCausalCarouselModal}
          setShowModal={() => setShowCausalCarouselModal(false)}
        />{" "}
      </div>
      <div className="flex w-full justify-center gap-4">
        <div className="relative flex w-1/2 items-center justify-center">
          {/* <Tooltip text="Immediately run the experiment with default values" /> */}
          <button
            className={`group relative font-roboto w-full bg-white hover:bg-[#ECEEF9] text-base boxshadow-feeling-lucky font-normal flex justify-center items-center gap-2 py-2 text-[#101828] rounded-lg shadow-sm border-[#06A7CA] border-[1px] ${
              question === "" || isLuckyLoading
                ? "cursor-not-allowed opacity-70"
                : ""
            }`}
            onMouseDown={onFeelingLucky}
            disabled={question === "" || isLuckyLoading}
          >
            <Zap className="w-5 h-5 text-[#06A7CA] boxshadow-feeling-lucky" />
            <span id="ask-question">
              {isLuckyLoading ? "Submitting..." : "I'm feeling lucky"}
            </span>

            <Tooltip
              message="Jump straight to results! Press to run the experiment instantly using default values for the population, attributes, levels, location and time: No configuration needed!"
              position={"bottom"}
            />
          </button>
        </div>
        <button
          id="ideation-guide-6"
          className={`font-roboto text-base font-semibold py-2.5 ${
            question === ""
              ? "bg-[#ECEDFB] cursor-not-allowed"
              : "bg-primary hover:bg-primary-dark"
          } text-white rounded-lg shadow-sm w-1/2`}
          onMouseDown={onContinue}
          disabled={question === ""}
        >
          <span id="ask-question">{"Continue"}</span>
        </button>
      </div>
      <SubscribeModal
        showModal={
          showSubscribeModal &&
          !roles.includes("employee") &&
          !roles.includes("customer")
        }
        setShowModal={setShowSubscribeModal}
      />
    </div>
  );
};

export default WhyComponent;
