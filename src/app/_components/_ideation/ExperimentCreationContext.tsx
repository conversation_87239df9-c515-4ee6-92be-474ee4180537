/* eslint-disable no-unused-vars */
"use client";
import React from "react";
import {
  BrandAttributeCombination,
  Country,
  DisplayAttribute,
  DisplayTrait,
  FileState,
  LLMModel,
  PopulationTraits,
} from "../../_components/_ideation/objects";

interface ExperimentCreationContextInterface {
  question: string;
  focus: string;
  activeSpecialist: string;
  displayTraits: DisplayTrait[];
  populationTraits: PopulationTraits;
  displayAttributes: DisplayAttribute[];
  realWorlBrandAttributeCombinations: BrandAttributeCombination[];
  when: string;
  where: Country | undefined;
  selectedLlmModel: LLMModel;
  selectedState: string | null;
  validatedQuestions: string[];
  setValidatedQuestions: (questions: string[] | ((prev: string[]) => string[])) => void;
  setSelectedLlmModel: (model: LLMModel) => void;
  setQuestion: (question: string) => void;
  setFocus: (focus: string) => void;
  setActiveSpecialist: (specialist: string) => void;
  setDisplayTraits: (displayTraits: DisplayTrait[]) => void;
  setPopulationTraits: (populationTrait: PopulationTraits) => void;
  setDisplayAttributes: (displayAttributes: DisplayAttribute[]) => void;
  fileState: FileState;
  setFileState: (fileState: FileState) => void;
  setRealWorldBrandAttributeCombinations: (
    brandattrComb: BrandAttributeCombination[]
  ) => void;
  setWhen: (when: string) => void;
  setWhere: (where: Country) => void;
  setSelectedState: (state: string | null) => void;
  productExists: boolean;
  setProductExists: (productExists: boolean) => void;
}

const ExperimentCreationContext =
  React.createContext<ExperimentCreationContextInterface>({
    question: "",
    focus: "",
    activeSpecialist: "",
    displayTraits: [],
    populationTraits: {
      state: null,
      age: [],
      household_income: [],
      gender: [],
      education_level: [],
      number_of_children: [],
      racial_group: [],
    },
    displayAttributes: [],
    realWorlBrandAttributeCombinations: [],
    when: new Date().getFullYear().toString(),
    where: undefined,
    selectedLlmModel: { name: "gpt4" },
    selectedState: "",
    validatedQuestions: [],
    setValidatedQuestions: () => {},
    setSelectedLlmModel: (model: LLMModel) => {},
    setQuestion: () => {},
    setFocus: () => {},
    setActiveSpecialist: (specialist: string) => {},
    setDisplayTraits: (displayTraits: DisplayTrait[]) => {},
    setPopulationTraits: (populationTrait: PopulationTraits) => {},
    setDisplayAttributes: (displayAttributes: DisplayAttribute[]) => {},
    setRealWorldBrandAttributeCombinations: (
      brandattrComb: BrandAttributeCombination[]
    ) => {},
    setWhen: (when: string) => {},
    setWhere: (where: Country) => {},
    setSelectedState: () => {},
    productExists: false,
    setProductExists: (productExists: boolean) => {},
    fileState: { file: null, data: null, error: null },
    setFileState: (fileState: FileState) => {},
  });

export const ExperimentCreationContextProvider =
  ExperimentCreationContext.Provider;
export default ExperimentCreationContext;
