import React from "react";
import { render, fireEvent, act } from "@testing-library/react";
import WhatComponent from "../WhatComponent";
import { UserProvider } from "@auth0/nextjs-auth0/client";
import { Auth0TokenProvider } from "../../_util/Auth0TokenContext";
import { SubscriptionProvider } from "../../_payments/SubscriptionContext";

describe("WhatComponent", () => {
  it('should call onBack when "Back" button is clicked', async () => {
    const onBackMock = jest.fn();
    const onRunExperimentMock = jest.fn();
    const setExperimentPrivateMock = jest.fn();
    const setExperimentPublicMock = jest.fn();

    const { getByText } = render(
      <UserProvider>
        <Auth0TokenProvider>
          <SubscriptionProvider>
            <WhatComponent
              setExperimentPrivate={setExperimentPrivateMock}
              setExperimentPublic={setExperimentPublicMock}
              onSubmit={onRunExperimentMock}
              onBack={onBackMock}
            />
          </SubscriptionProvider>
        </Auth0TokenProvider>
      </UserProvider>
    );

    await act(async () => {
      fireEvent.mouseDown(getByText("Back"));
    });

    expect(onBackMock).toHaveBeenCalledTimes(1);
    expect(onRunExperimentMock).not.toHaveBeenCalled();
  });
});
