import Image from "next/image";
import { ReactElement } from "react";
interface SpecialistCardProps {
  title: string;
  icon: ReactElement<any, any>;
  active: boolean;
  onMouseDown: () => void;
}

const SpecialistCard = ({
  title,
  icon,
  active,
  onMouseDown,
}: SpecialistCardProps) => {
  return (
    <div
      className={`flex flex-row gap-2 px-4 py-2 items-center justify-center border rounded-lg hover:cursor-pointer ${active ? "bg-[#CCCEFA]/30 border-primary" : "bg-white border-[#D0D5DD]"}`}
      onMouseDown={onMouseDown}
    >
      {/* <Image src={icon} height={20} width={20} alt={title + " Icon"} /> */}
      {icon}
      <p
        className={`text-text-dark font-roboto text-base leading-7 ${active ? "font-medium" : "font-normal"}`}
      >
        {title}
      </p>
    </div>
  );
};

export default SpecialistCard;
