import React, { useEffect, useMemo, useState } from "react";
import { ValidationResponse, FinalSelectedPopulationTraits } from "../objects";
import { Listbox } from "@headlessui/react";
import { ChevronDown } from "lucide-react";

interface ValidationResultsViewProps {
  data: ValidationResponse;
  onDataChange: (finalTraits: FinalSelectedPopulationTraits) => void;
}

export default function ValidationResultsView({
  data,
  onDataChange,
}: ValidationResultsViewProps): React.JSX.Element {
  const [selectedOption, setSelectedOption] = useState<
    "suggestion" | "original" | "one_trait_change"
  >(() => {
    const storedOption = localStorage.getItem("baseOption");
    return storedOption === "suggestion" ||
      storedOption === "original" ||
      storedOption === "one_trait_change"
      ? (storedOption as "suggestion" | "original" | "one_trait_change")
      : "suggestion";
  });

  const [selectedTrait, setSelectedTrait] = useState<string | null>(() => {
    const storedSelectedTrait = localStorage.getItem("selectedTrait");
    return storedSelectedTrait || null;
  });

  const handleOptionChange = (
    option: "suggestion" | "original" | "one_trait_change"
  ) => {
    setSelectedOption(option);
    localStorage.setItem("baseOption", option);

    if (option !== "one_trait_change") {
      setSelectedTrait(null);
      localStorage.removeItem("selectedTrait");
    }
  };

  const handleTraitChange = (trait: string) => {
    setSelectedTrait(trait || null);
    localStorage.setItem("selectedTrait", trait || "");
  };

  const displayedData = useMemo<FinalSelectedPopulationTraits>(() => {
    if (selectedOption === "one_trait_change" && selectedTrait) {
      const traitData = data.one_trait_change[selectedTrait];

      // Create a copy of the original data as the base
      const baseData = { ...data.original };

      // If traitData.population_traits is an array, update just that one trait
      if (
        Array.isArray(traitData.population_traits) ||
        traitData.population_traits === null
      ) {
        return {
          population_size: traitData.population_size,
          population_traits: {
            ...baseData.population_traits,
            [selectedTrait]: traitData.population_traits,
          },
        };
      } else {
        // Otherwise use the entire trait data
        return traitData;
      }
    }

    // Return the selected dataset (suggestion or original)
    return data[
      selectedOption === "one_trait_change" ? "original" : selectedOption
    ];
  }, [data, selectedOption, selectedTrait]);

  // Determine if the continue button should be disabled
  const isDisabled = displayedData.population_size === 0;

  useEffect(() => {
    if (displayedData && onDataChange) {
      onDataChange(displayedData);
    }
  }, [displayedData, onDataChange]);

  // Force a trait selection when "one_trait_change" is selected but no trait is chosen
  useEffect(() => {
    if (
      selectedOption === "one_trait_change" &&
      !selectedTrait &&
      data.one_trait_change
    ) {
      const traitKeys = Object.keys(data.one_trait_change);
      if (traitKeys.length > 0) {
        const defaultTrait = traitKeys[0];
        setSelectedTrait(defaultTrait);
        localStorage.setItem("selectedTrait", defaultTrait);
      }
    }
  }, [selectedOption, selectedTrait, data.one_trait_change]);

  return (
    <div className="flex flex-col gap-6 max-w-6xl mx-auto">
      {/* Option Selection Controls */}
      <div className="mb-4 flex flex-col gap-3">
        <h3 className="text-sm font-medium">Select population data to use:</h3>

        <div className="flex gap-4 flex-wrap">
          <label className="flex items-center cursor-pointer gap-2">
            <input
              type="checkbox"
              checked={selectedOption === "suggestion"}
              onChange={() => handleOptionChange("suggestion")}
              className="w-4 h-4 accent-[#232353]"
            />
            <span className="text-sm">
              Recommended (Population size: {data.suggestion.population_size})
            </span>
          </label>

          <label className="flex items-center cursor-pointer gap-2">
            <input
              type="checkbox"
              checked={selectedOption === "original"}
              onChange={() => handleOptionChange("original")}
              className="w-4 h-4 accent-[#232353]"
            />
            <span className="text-sm">
              Original (Population size: {data.original.population_size})
            </span>
          </label>

          <label className="flex items-center cursor-pointer gap-2">
            <input
              type="checkbox"
              checked={selectedOption === "one_trait_change"}
              onChange={() => handleOptionChange("one_trait_change")}
              className="w-4 h-4 accent-[#232353]"
            />
            <span className="text-sm">One Trait Change</span>
          </label>
        </div>
      </div>

      {/* Trait Selector - Only show when one_trait_change is selected */}
      {selectedOption === "one_trait_change" && (
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Select One Trait to Change:
          </label>
          <Listbox value={selectedTrait} onChange={handleTraitChange}>
            <div className="relative">
              <Listbox.Button className="w-full p-2 border border-gray-300 rounded focus:border-[#232353] focus:ring-2 focus:ring-[#4c4f99] flex justify-between items-center relative">
                <span>
                  {selectedTrait
                    ? selectedTrait
                        .replace(/_/g, " ")
                        .replace(/^./, (m) => m.toUpperCase())
                    : "-- Select a trait --"}
                </span>
                <ChevronDown className="h-4 w-4 text-gray-500" />
              </Listbox.Button>
              <Listbox.Options className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded shadow-lg max-h-60 overflow-auto">
                <Listbox.Option key="" value="">
                  {({ active }) => (
                    <div
                      className={`cursor-pointer select-none p-2 text-center ${active ? "bg-gray-200" : ""}`}
                    >
                      -- Select a trait --
                    </div>
                  )}
                </Listbox.Option>
                {Object.keys(data.one_trait_change).map((trait) => (
                  <Listbox.Option key={trait} value={trait}>
                    {({ active }) => (
                      <div
                        className={`cursor-pointer select-none p-2 ${active ? "bg-gray-200" : ""}`}
                      >
                        {trait
                          .replace(/_/g, " ")
                          .replace(/^./, (m) => m.toUpperCase())}

                        <span className="text-xs text-gray-500 ml-2">
                          (Size: {data.one_trait_change[trait].population_size})
                        </span>
                      </div>
                    )}
                  </Listbox.Option>
                ))}
              </Listbox.Options>
            </div>
          </Listbox>
        </div>
      )}

      {/* Display Data */}
      <div className="p-4 border rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold mb-3 flex items-center">
          <span>Population Size: {displayedData.population_size}</span>
          {selectedOption === "suggestion" && (
            <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
              Recommended
            </span>
          )}
        </h3>
        <ul className="mt-2 divide-y divide-gray-200">
          {Object.entries(displayedData.population_traits).map(
            ([key, value]) => (
              <li
                key={key}
                className={`py-3 ${
                  selectedTrait === key && selectedOption === "one_trait_change"
                    ? "bg-yellow-50 -mx-4 px-4"
                    : ""
                }`}
              >
                <div className="flex justify-between items-start">
                  <strong>
                    {key
                      .replace(/_/g, " ")
                      .replace(/^./, (m) => m.toUpperCase())}
                    :
                  </strong>
                  <div className="text-right max-w-[60%]">
                    {Array.isArray(value) ? (
                      value.length === 2 &&
                      (key === "age" || key === "household_income") ? (
                        // Handle range values
                        <span>
                          {key === "household_income"
                            ? `$${value[0].toLocaleString()} - $${value[1].toLocaleString()}`
                            : `${value[0]} - ${value[1]}`}
                        </span>
                      ) : (
                        // Handle list values
                        <span className="flex flex-wrap justify-end gap-1">
                          {value.map((item, index) => (
                            <span
                              key={index}
                              className="bg-gray-100 px-2 py-1 rounded text-xs"
                            >
                              {item}
                            </span>
                          ))}
                        </span>
                      )
                    ) : (
                      value
                    )}
                  </div>
                </div>
              </li>
            )
          )}
        </ul>
      </div>
    </div>
  );
}
