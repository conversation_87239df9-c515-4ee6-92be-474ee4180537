import { CheckIcon } from "@heroicons/react/20/solid";

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

interface ProgressIndicatorProps {
  steps: {
    name: string;
    status: string;
  }[];
  // eslint-disable-next-line no-unused-vars
  setCurrStep: (step: number) => void;
}

const ProgressIndicator = ({ steps, setCurrStep }: ProgressIndicatorProps) => {
  return (
    <div className="flex flex-cols justify-center h-full">
      <ol role="list" className="flex items-center">
        {steps.map((step, stepIdx) => (
          <li key={step.name} className="w-28 relative pr-8 sm:pr-20">
            {step.status === "complete" ? (
              <>
                <div
                  className="absolute inset-0 -top-5 flex items-center"
                  aria-hidden="true"
                >
                  <div
                    className={`${stepIdx === steps.length - 1 ? "h-0" : "h-0.5"} w-full bg-gray-200`}
                  />
                </div>
                <div className="flex flex-col items-center">
                  <button
                    onMouseDown={() => setCurrStep(stepIdx)}
                    className="relative flex h-8 w-8 items-center justify-center rounded-full bg-primary hover:bg-text-dark"
                  >
                    <CheckIcon
                      className="h-5 w-5 text-white"
                      aria-hidden="true"
                    />
                  </button>
                  <p className="text-sm text-primary font-roboto text-center ">
                    {step.name}
                  </p>
                </div>
              </>
            ) : step.status === "current" ? (
              <>
                <div
                  className="absolute inset-0 -top-5 flex items-center"
                  aria-hidden="true"
                >
                  <div
                    className={`${stepIdx === steps.length - 1 ? "h-0" : "h-0.5"} w-full bg-gray-200`}
                  />
                </div>
                <div className="flex flex-col items-center">
                  <button
                    onMouseDown={() => setCurrStep(stepIdx)}
                    className="relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-primary bg-white"
                    aria-current="step"
                  >
                    <span
                      className="h-2.5 w-2.5 rounded-full bg-primary"
                      aria-hidden="true"
                    />
                    <span className="sr-only">{step.name}</span>
                  </button>
                  <p
                    className={`${stepIdx === steps.length - 1 ? "" : "ml-0"} text-sm text-primary font-roboto text-center`}
                  >
                    {step.name}
                  </p>
                </div>
              </>
            ) : (
              <>
                <div
                  className="absolute inset-0 -top-5 flex items-center"
                  aria-hidden="true"
                >
                  <div
                    className={`${stepIdx === steps.length - 1 ? "h-0" : "h-0.5"} w-full bg-gray-200`}
                  />
                </div>
                <div className="flex flex-col items-center">
                  <a
                    href="#"
                    className="group relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-300 bg-white hover:border-gray-400"
                  >
                    <span
                      className="h-2.5 w-2.5 rounded-full bg-transparent group-hover:bg-gray-300"
                      aria-hidden="true"
                    />
                  </a>
                  <p
                    className={`${stepIdx === steps.length - 1 ? "" : "ml-0"} text-sm text-primary font-roboto text-center`}
                  >
                    {step.name}
                  </p>
                </div>
              </>
            )}
          </li>
        ))}
      </ol>
    </div>
  );
};

export default ProgressIndicator;
