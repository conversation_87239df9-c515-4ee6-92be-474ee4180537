/* eslint-disable no-unused-vars */
/* eslint-disable prettier/prettier */
import React, {
  useCallback,
  useContext,
  useDebugValue,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import SpecialistCard from "./_who/SpecialistCard";
import TraitCard from "./_who/TraitCard";
import {
  SPECIALISTS,
  US_SPECIALIST_TRAITS,
  NON_US_SPECIALIST_TRAITS,
} from "./constants";
import {
  DisplayTrait,
  Attribute,
  ProductLevelsRequest,
  attributesLevelsRequest,
  orthogonalAttributesLevelsRequest,
  DisplayAttribute,
  RealWorldAttributesResponse,
  AttributeResponse,
  PopulationTraits,
  ValidationResponse,
  FinalSelectedPopulationTraits,
  StateData,
} from "./objects";
import ExperimentCreationContext from "./ExperimentCreationContext";
import { ErrorMessage } from "@/app/utils/errorMessage";
import { usePathname } from "next/navigation";
import AddNewTraitCard from "./_who/AddNewTraitCard";
import {
  UserSearch,
  TriangleAlert,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import PopulationTraitValue from "./_who/PopulationTraitValue";
import SliderComponent from "./SliderComponent";
import {
  education,
  gender,
  children,
  race,
} from "./_whenwhere/PopulationTraitsData";
import { States } from "./_whenwhere/PopulationTraitsData";
import ValidationResultsView from "./_who/ValidationResultsView";
import { ApiStatusContext } from "./_who/ApiStatusContext";
import ResearchQuestionSection from "./ResearchQuestionSection";

const initializeStateData = (
  state: string | null,
  isUSA: boolean
): StateData => {
  if (!isUSA) {
    return {
      Age: {
        min: 0,
        max: 0,
        "90th_percentile": 0,
        "95th_percentile": 0,
      },
      "Household income": {
        min: 0,
        max: 0,
        "90th_percentile": 0,
        "95th_percentile": 0,
      },
    };
  }

  // If no state is selected or state is 'USA', use the USA aggregate data
  if (!state || state === "USA") {
    return {
      Age: States.USA.Age,
      "Household income": States.USA["Household income"],
    };
  }

  // Get base state data from the States object
  const baseStateData = States[state as keyof typeof States];

  // For non-USA or when there's no state data, return default values
  if (!baseStateData) {
    return {
      Age: {
        min: 18,
        max: 65,
        "90th_percentile": 60,
        "95th_percentile": 65,
      },
      "Household income": {
        min: 0,
        max: 100000,
        "90th_percentile": 90000,
        "95th_percentile": 100000,
      },
    };
  }

  // Return the base state data (don't modify with user selections here)
  return {
    Age: baseStateData.Age,
    "Household income": baseStateData["Household income"],
  };
};

interface WhoComponentProps {
  onComplete: () => void;
  onBack: () => void;
  existingYear: String | null;
  existingCountry: String | null;
  existingQuestion: String | null;
  setPopulationTraits: (traits: any) => void;
  transformedAttributes: (attrs: AttributeResponse[]) => DisplayAttribute[];
  previousStep: number;
  currentStep: "first" | "second" | "third";
  setCurrentStep: (step: "first" | "second" | "third") => void;
}

const WhoComponent: React.FC<WhoComponentProps> = ({
  onComplete,
  onBack,
  setPopulationTraits,
  existingYear,
  existingCountry,
  existingQuestion,
  transformedAttributes,
  currentStep,
  setCurrentStep,
}) => {
  const {
    activeSpecialist,
    setActiveSpecialist,
    displayTraits,
    setDisplayTraits,
    setDisplayAttributes,
    question,
    when,
    where,
    displayAttributes,
    selectedState,
    setRealWorldBrandAttributeCombinations,
    setProductExists,
    selectedLlmModel,
  } = useContext(ExperimentCreationContext);
  const { isApiInProgress, isApiFailed, setIsApiInProgress, setIsApiFailed } =
    useContext(ApiStatusContext);

  const [isUSA, setIsUSA] = useState(
    where?.name === "United States of America (USA)"
  );

  // Add effect to update isUSA when country changes
  useEffect(() => {
    setIsUSA(where?.name === "United States of America (USA)");
  }, [where?.name]);

  const [isLoading, setIsLoading] = useState(false);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [loadingMessage, setLoadingMessage] = useState("");
  const [noTraitsSelected, setNoTraitsSelected] = useState(false);
  const [apiFailure, setApiFailure] = useState(false);
  const isInitialMount = useRef(true);
  const apiCallMade = useRef(false);
  const [dataFetched, setDataFetched] = useState(() => {
    const storedValue = localStorage.getItem("dataFetched");
    return storedValue === "true" ? true : false;
  });
  const pathname = usePathname();
  // State to track if trait card is visible
  const [showTraitCard, setShowTraitCard] = useState(true);

  const [stateData, setStateData] = useState(() =>
    initializeStateData(selectedState || "", isUSA)
  );

  const [validationLoading, setValidationLoading] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [populationSizeCount, setPopulationSizeCount] = useState<number>(() => {
    const storedPopulationSizeCount = localStorage.getItem(
      "populationSizeCount"
    );
    return storedPopulationSizeCount
      ? JSON.parse(storedPopulationSizeCount)
      : 0;
  });
  const [currentPopulationSize, setCurrentPopulationSize] = useState<number>(0);

  const [selectedPopulationTraits, setSelectedPopulationTraits] =
    useState<FinalSelectedPopulationTraits | null>(null);

  const [validationResults, setValidationResults] =
    useState<ValidationResponse | null>(null);

  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({
    customTraits: false,
    usTraits: false,
    nonUsTraits: false,
    lifestyleTraits: false,
    psychologicalTraits: false,
    personalityTraits: false,
  });
  const [accessToken, setAccessToken] = useState<string | null>(null);

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const response = await fetch("/api/token");
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setAccessToken(data.accessToken);
      } catch (error) {
        console.error("Failed to fetch access token:", error);
      }
    };

    fetchToken();
  }, []);

  const toggleSection = (category: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const groupTraitsByCategory = (traits: DisplayTrait[], isUSA: boolean) => {
    const currentIsUSA = where?.name === "United States of America (USA)";
    const grouped = {
      usTraits: [] as DisplayTrait[],
      nonUsTraits: [] as DisplayTrait[],
      lifestyleTraits: [] as DisplayTrait[],
      psychologicalTraits: [] as DisplayTrait[],
      personalityTraits: [] as DisplayTrait[],
      customTraits: [] as DisplayTrait[], // Add custom traits array
    };

    traits.forEach((trait) => {
      if (trait.category === "customTraits") {
        grouped.customTraits.push(trait);
      } else if (trait.category) {
        // Only include US traits if the country is USA
        if (trait.category === "usTraits" && !currentIsUSA) {
          return;
        }
        // Only include non-US traits if the country is not USA
        if (trait.category === "nonUsTraits" && currentIsUSA) {
          return;
        }
        grouped[trait.category].push(trait);
      } else {
        grouped.personalityTraits.push(trait);
      }
    });

    return grouped;
  };

  const categoryDisplayNames = {
    usTraits: "Demographic Traits",
    nonUsTraits: "Demographic Traits",
    lifestyleTraits: "Lifestyle Characteristics",
    psychologicalTraits: "Psychological Traits",
    personalityTraits: "Personality Traits",
    customTraits: "Custom Traits",
  };

  const clearLocalStorageTraits = () => {
    const traitKeys = [
      "age",
      "income",
      "education",
      "gender",
      "children",
      "race",
    ];
    traitKeys.forEach((key) => localStorage.removeItem(key));

    // Set a flag indicating traits were reset
    localStorage.setItem("traitsReset", "true");
  };

  const previousState = localStorage.getItem("previousSelectedState");
  const handleStateChange = useCallback((newState: string | null) => {
    if (previousState !== newState) {
      // Only clear traits if the state actually changed
      clearLocalStorageTraits();
      localStorage.removeItem("ageRange");
      localStorage.removeItem("incomeRange");

      // If state is 'USA' or null, use the USA aggregate data
      const baseStateData =
        newState === "USA" || !newState
          ? States.USA
          : States[newState as keyof typeof States];

      setStateData({
        Age: baseStateData.Age,
        "Household income": baseStateData["Household income"],
      });

      localStorage.setItem("previousSelectedState", newState || "");

      // Re-initialize traits with default values
      localStorage.setItem("education", JSON.stringify(education));
      localStorage.setItem("gender", JSON.stringify(gender));
      localStorage.setItem("children", JSON.stringify(children));
      localStorage.setItem("race", JSON.stringify(race));
    }
  }, []);

  useEffect(() => {
    handleStateChange(selectedState);
  }, [selectedState, handleStateChange]);

  const handleGetAgeRange = useCallback((ageRange: number[]) => {
    saveToLocalStorage("age", ageRange);
  }, []);

  const handleGetIncomeRange = useCallback((incomeRange: number[]) => {
    saveToLocalStorage("income", incomeRange);
  }, []);

  const saveToLocalStorage = (key: string, value: number[] | string[]) => {
    if (typeof window !== "undefined") {
      localStorage.setItem(key, JSON.stringify(value));
    }
  };

  useEffect(() => {
    // Only initialize the localStorage values if they don't already exist
    if (!localStorage.getItem("education") || previousState !== selectedState) {
      localStorage.setItem("education", JSON.stringify(education));
    }
    if (!localStorage.getItem("gender") || previousState !== selectedState) {
      localStorage.setItem("gender", JSON.stringify(gender));
    }
    if (!localStorage.getItem("children") || previousState !== selectedState) {
      localStorage.setItem("children", JSON.stringify(children));
    }
    if (!localStorage.getItem("race") || previousState !== selectedState) {
      localStorage.setItem("race", JSON.stringify(race));
    }
  }, [selectedState]);

  const handleTraitValueChange = useCallback((traitType: string) => {
    const selectedValues: string[] = [];

    return (traitValue: string, activeStatus: boolean) => {
      const storedValues = localStorage.getItem(traitType);
      if (storedValues) {
        selectedValues.length = 0;
        selectedValues.push(...JSON.parse(storedValues));
      }

      if (activeStatus && !selectedValues.includes(traitValue)) {
        selectedValues.push(traitValue);
      } else if (!activeStatus) {
        const indexToRemove = selectedValues.indexOf(traitValue);
        if (indexToRemove !== -1) {
          selectedValues.splice(indexToRemove, 1);
        }
      }

      localStorage.setItem(traitType, JSON.stringify(selectedValues));
    };
  }, []);

  const getEduValue = useMemo(
    () => handleTraitValueChange("education"),
    [handleTraitValueChange]
  );
  const getGenderValue = useMemo(
    () => handleTraitValueChange("gender"),
    [handleTraitValueChange]
  );
  const getChildrenValue = useMemo(
    () => handleTraitValueChange("children"),
    [handleTraitValueChange]
  );
  const getRaceValue = useMemo(
    () => handleTraitValueChange("race"),
    [handleTraitValueChange]
  );

  useEffect(() => {
    const storedValidationResults = localStorage.getItem("validationResults");
    if (storedValidationResults) {
      setValidationResults(JSON.parse(storedValidationResults));
    }
  }, []);

  const getCurrentTraits = useCallback((): PopulationTraits => {
    const age = JSON.parse(localStorage.getItem("age") || "[18, 65]");
    const household_income = JSON.parse(
      localStorage.getItem("income") || "[0, 100000]"
    );
    const education_level = JSON.parse(
      localStorage.getItem("education") || "[]"
    );
    const gender = JSON.parse(localStorage.getItem("gender") || "[]");
    const number_of_children = JSON.parse(
      localStorage.getItem("children") || "[]"
    );
    const racial_group = JSON.parse(localStorage.getItem("race") || "[]");

    return {
      age,
      household_income,
      education_level,
      gender,
      number_of_children,
      racial_group,
      state: selectedState && selectedState !== "USA" ? selectedState : null,
    };
  }, [selectedState]);

  const getErrorMessage = () => {
    if (
      currentStep === "first" &&
      validationError === "Failed to validate population traits"
    ) {
      return {
        message: validationError,
        showTryAgain: true,
      };
    }

    if (
      currentStep === "second" &&
      validationError === "Select a non-zero population configuration"
    ) {
      return {
        message: validationError,
        showTryAgain: false,
      };
    }

    if (errorMessage && currentStep === "third") {
      return {
        message: errorMessage,
        showTryAgain: false,
      };
    }

    return null;
  };

  const handleValidation = async () => {
    try {
      setValidationLoading(true);
      setValidationError(null);

      const currentTraits = getCurrentTraits();

      const payload = {
        age: currentTraits.age,
        education_level: currentTraits.education_level,
        gender: currentTraits.gender.map(
          (g) => g.charAt(0).toUpperCase() + g.slice(1).toLowerCase()
        ),
        household_income: currentTraits.household_income,
        number_of_children: currentTraits.number_of_children,
        number_of_records: 300,
        racial_group: currentTraits.racial_group,
        state: currentTraits.state,
      };

      const validatePopulationTraits = async () => {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/populations/validate`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify(payload),
          }
        );

        if (!response.ok) {
          throw new Error(`Validation failed with status: ${response.status}`);
        }

        const data = await response.json();

        if (!data || !data.original || !data.original.population_size) {
          throw new Error("Invalid response data from validation endpoint");
        }

        return data;
      };

      const data = await retryFetch(validatePopulationTraits, 2);

      setValidationResults(data);
      setPopulationSizeCount(data.original.population_size);
      localStorage.setItem("validationResults", JSON.stringify(data));
      localStorage.setItem(
        "lastValidatedTraits",
        JSON.stringify(currentTraits)
      );
      localStorage.setItem(
        "populationSizeCount",
        JSON.stringify(data.original.population_size)
      );
      if (data.original.population_size <= 300) {
        setCurrentStep("second");
      } else {
        setCurrentStep("third");
        setProductExists(data.original);
        setPopulationTraits(data.original);
      }
    } catch (error) {
      setValidationError("Failed to validate population traits");
    } finally {
      setValidationLoading(false);
    }
  };

  const handleBack = () => {
    setValidationError(null);

    if (currentStep === "second") {
      setCurrentStep("first");
    } else if (currentStep === "third") {
      if (!isUSA) {
        onBack();
      }
      if (populationSizeCount < 300) {
        setCurrentStep("second");
      } else {
        setCurrentStep("first");
      }
    } else {
      onBack();
    }
  };

  const handleContinue = () => {
    setValidationError(null);

    if (currentStep === "first" && isUSA) {
      const currentTraits = getCurrentTraits();
      const currentTraitsString = JSON.stringify(currentTraits);
      const lastTraitsString = localStorage.getItem("lastValidatedTraits");

      if (currentTraitsString !== lastTraitsString) {
        handleValidation();
        localStorage.removeItem("validationResults");
        localStorage.removeItem("lastValidatedTraits");
        localStorage.removeItem("baseOption");
        localStorage.removeItem("selectedTrait");
      } else {
        const storedResults = localStorage.getItem("validationResults");
        if (storedResults) {
          setValidationResults(JSON.parse(storedResults));
        }
        if (populationSizeCount <= 300) {
          setCurrentStep("second");
        } else {
          setCurrentStep("third");
        }
      }
    } else if (currentStep === "second") {
      if (currentPopulationSize === 0) {
        setValidationError("Select a non-zero population configuration");
        return;
      }
      // Only set population traits if they exist
      if (selectedPopulationTraits?.population_traits) {
        setPopulationTraits(selectedPopulationTraits.population_traits);
      }
      setCurrentStep("third");
    } else {
      if (!continueDisabled()) {
        // Ensure we have population traits before setting and completing
        if (selectedPopulationTraits?.population_traits) {
          setPopulationTraits(selectedPopulationTraits.population_traits);
        } else {
          // Use the current traits as fallback
          const currentTraits = getCurrentTraits();
          if (currentTraits) {
            setPopulationTraits(currentTraits);
          }
        }
        onComplete();
      }
    }
  };

  useEffect(() => {
    setValidationError(null);
  }, [currentStep, selectedPopulationTraits]);

  const handleFinalTraits = (
    finalData: FinalSelectedPopulationTraits
  ): void => {
    const StoredPopulationSize = localStorage.getItem("validationResults");
    if (StoredPopulationSize) {
      const parsedSize = JSON.parse(StoredPopulationSize);
      setPopulationSizeCount(parsedSize.original.population_size);
    }

    setCurrentPopulationSize(finalData.population_size);
    setSelectedPopulationTraits(finalData);

    // Store the selected population traits in localStorage for backup
    if (finalData.population_traits) {
      localStorage.setItem(
        "selectedPopulationTraits",
        JSON.stringify(finalData.population_traits)
      );
    }
  };

  const retryFetch = async (fetchFunction: () => Promise<any>, retries = 3) => {
    for (let i = 0; i <= retries; i++) {
      try {
        return await fetchFunction();
      } catch (error) {
        console.log(`Retry attempt ${i + 1}/${retries + 1} failed:`, error);
        if (i === retries) throw error;
      }
    }
  };

  const fetchData = async () => {
    if (apiCallMade.current) return;
    apiCallMade.current = true;

    if (isApiInProgress) {
      return; // Skip if already in progress
    }

    // Check if user has already uploaded attributes via CSV
    if (displayAttributes && displayAttributes.length > 0) {
      setIsLoading(false);
      setIsApiInProgress(false);
      return; // Skip API call if attributes already exist
    }

    setIsLoading(true);
    setErrorMessage(null);
    setApiFailure(false);
    setIsApiInProgress(true);
    setIsApiFailed(false);
    if (
      question === existingQuestion &&
      when === existingYear &&
      where?.name === existingCountry
    ) {
      setIsLoading(false);
      return;
    }

    try {
      let attributes: AttributeResponse[];
      setDisplayAttributes([]);
      const fetchAttributesLevels = async () => {
        const attributesLevelsBody: attributesLevelsRequest = {
          why_prompt: question,
          country: where?.name,
          level_count: 5,
          attribute_count: 7,
          year: when,
        };

        const attributesLevelsResponse = await fetch("/api/attributes-levels", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(attributesLevelsBody),
        });

        if (!attributesLevelsResponse.ok) {
          setDisplayAttributes([]); //if the request fails, clear the displayAttributes
          throw new Error(
            `Error fetching attributes levels: ${attributesLevelsResponse.status}`
          );
        }

        const data = await attributesLevelsResponse.json();

        // Verify that the response contains the expected data
        if (!data || !data.attributes_levels) {
          throw new Error(
            "Invalid response data from attributes-levels endpoint"
          );
        }

        return data;
      };

      const response = await retryFetch(fetchAttributesLevels, 3);
      const { attributes_levels, brand_attribute_combinations } = response;

      setRealWorldBrandAttributeCombinations(
        brand_attribute_combinations || []
      );

      attributes = [...attributes_levels];
      setDisplayAttributes(transformedAttributes(attributes));
    } catch (error: any) {
      setIsApiFailed(true);
      console.error("Error fetching data:", error);
      setErrorMessage(ErrorMessage.attributesFetch);
      setApiFailure(true);
    } finally {
      setIsApiInProgress(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    localStorage.setItem("dataFetched", dataFetched.toString());
  }, [dataFetched]);

  function shouldCallApi(pathname: string | null): boolean {
    if (!pathname) {
      return false;
    }
    
    if (pathname === "/ideation") {
      return true;
    }

    const parts = pathname.split("/");
    const id = parts[2];

    if (id && id.length > 15) {
      return true;
    }

    return false;
  }

  useEffect(() => {
    const fetchDataIfNeeded = () => {
      const lastQuestion = localStorage.getItem("lastQuestion");
      const lastWhere = localStorage.getItem("lastWhere");
      const currentWhere = where ? JSON.stringify(where.name) : "";
      const replicateRetryQuestion = localStorage.getItem(
        "replicateRetryQuestion"
      );
      const replicateRetryWhere = JSON.stringify(
        localStorage.getItem("replicateRetryWhere")
      );
      const storedAttributes = localStorage.getItem("attributes");

      // Check if user has provided attributes via CSV
      if (displayAttributes && displayAttributes.length > 0) {
        // User has already provided attributes via CSV, don't fetch from API
        setDataFetched(true);
        localStorage.setItem("lastQuestion", question);
        localStorage.setItem("lastWhere", currentWhere);
        return;
      }

      const shouldFetchData =
        !dataFetched ||
        question !== lastQuestion ||
        currentWhere !== lastWhere ||
        displayAttributes.length === 0;

      if (
        storedAttributes &&
        shouldCallApi(pathname) &&
        replicateRetryQuestion === question &&
        replicateRetryWhere === currentWhere
      ) {
        const parsedAttributes: DisplayAttribute[] =
          JSON.parse(storedAttributes);
        setDisplayAttributes(parsedAttributes);
        localStorage.setItem("lastQuestion", question);
        localStorage.setItem("lastWhere", currentWhere);
      } else if (shouldFetchData && shouldCallApi(pathname)) {
        // clearLocalStorageTraits()
        fetchData();
        setDataFetched(true);
        localStorage.setItem("lastQuestion", question);
        localStorage.setItem("lastWhere", currentWhere);
      }
      console.log(displayAttributes);
    };

    fetchDataIfNeeded();
  }, [dataFetched, question, where, pathname, displayAttributes.length]);

  const sortTraits = (traitsList: DisplayTrait[]) => {
    return traitsList.sort((trait1, trait2) =>
      trait1.active === trait2.active ? 0 : trait1.active ? -1 : 1
    );
  };

  const toggleSelectedTrait = (title: string) => {
    setDisplayTraits(
      displayTraits.map((trait) => {
        if (trait.title.toUpperCase() === title.toUpperCase()) {
          return {
            ...trait,
            active: !trait.active,
          };
        }
        return trait;
      })
    );
  };

  const selectSpecialist = (title: string) => {
    const currentIsUSA = where?.name === "United States of America (USA)";
    const specialistTraits = currentIsUSA
      ? US_SPECIALIST_TRAITS[
          title.replace(/\s+/g, "") as keyof typeof US_SPECIALIST_TRAITS
        ]
      : NON_US_SPECIALIST_TRAITS[
          title.replace(/\s+/g, "") as keyof typeof NON_US_SPECIALIST_TRAITS
        ];

    if (!specialistTraits) {
      console.error(`No traits found for specialist: ${title}`);
      return;
    }

    // Group traits by category
    const categorizedTraits = displayTraits.reduce(
      (acc, trait) => {
        const category = trait.category || "personalityTraits";
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(trait);
        return acc;
      },
      {} as Record<string, DisplayTrait[]>
    );

    // For each category, activate at most one trait that matches the specialist's traits
    const updatedTraits = displayTraits.map((trait) => {
      const category = trait.category || "personalityTraits";

      // Skip traits that don't match the current country type
      if (
        (category === "usTraits" && !currentIsUSA) ||
        (category === "nonUsTraits" && currentIsUSA)
      ) {
        return {
          ...trait,
          active: false,
        };
      }

      const traitList = categorizedTraits[category] || [];

      // Find which traits in this category match the specialist's traits
      const matchingTraitsInCategory = traitList.filter((t) =>
        specialistTraits.some(
          (st) => st.toUpperCase() === t.title.toUpperCase()
        )
      );

      // If this is the first matching trait in its category, activate it
      const isFirstMatchInCategory =
        matchingTraitsInCategory.length > 0 &&
        matchingTraitsInCategory[0].title.toUpperCase() ===
          trait.title.toUpperCase();

      return {
        ...trait,
        active: isFirstMatchInCategory,
      };
    });

    setDisplayTraits(sortTraits(updatedTraits));

    if (activeSpecialist === title) {
      setActiveSpecialist("");
    } else {
      setActiveSpecialist(title);
    }
  };

  useEffect(() => {
    if (displayTraits.length > 0 && activeSpecialist === "") {
      selectSpecialist("Economist");
    }
  }, [displayTraits, activeSpecialist, where?.name]); // Added where?.name to dependencies

  // Add effect to reselect specialist when country changes
  useEffect(() => {
    if (displayTraits.length > 0 && activeSpecialist) {
      // Reselect the current specialist to update traits based on new country
      selectSpecialist(activeSpecialist);
    }
  }, [where?.name]);

  const getNumSelected = () => {
    return displayTraits.filter((trait) => trait.active).length;
  };

  const continueDisabled = () => {
    return (
      (getNumSelected() < 1 && !isUSA) || // Only check trait selection for non-USA
      isLoading ||
      isApiInProgress ||
      apiFailure
    );
  };

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      if (displayTraits.length > 0 && !activeSpecialist) {
        selectSpecialist("Economist");
      }
    }
    if (
      getNumSelected() === 0 &&
      displayTraits.length > 0 &&
      activeSpecialist === ""
    ) {
      selectSpecialist("Economist");
    }
  }, [activeSpecialist, displayTraits.length, getNumSelected]);

  const addnewTrait = (title: string, values: string[]) => {
    setDisplayTraits([
      {
        title: title,
        values: values,
        active: true,
        category: "customTraits", // Set category for new traits
      },
      ...displayTraits,
    ]);
    setLoadingMessage("");
    if (displayAttributes.length) {
      setIsLoading(false);
    }
  };
  const activateNewTrait = (title: string) => {
    setDisplayTraits(
      displayTraits.map((trait) => {
        if (trait.title === title) {
          return {
            ...trait,
            active: true,
            category: trait.category || "customTraits",
          };
        }
        return trait;
      })
    );
  };

  const categoryOrder = useMemo(() => {
    const currentIsUSA = where?.name === "United States of America (USA)";
    return [
      "customTraits",
      currentIsUSA ? "usTraits" : "nonUsTraits",
      "lifestyleTraits",
      "psychologicalTraits",
      "personalityTraits",
    ] as const;
  }, [where?.name]);

  const handleDeleteTraitCard = () => {
    setShowTraitCard(false);
  };

  return (
    <>
      <div className="flex flex-col h-full w-9/12 max-w-4xl">
        {/* First View - Population Form */}
        {currentStep === "first" && isUSA && (
          <div className="flex flex-col gap-6">
            <div className="flex flex-col h-full">
              <div className="flex flex-col gap-6 items-start max-w-4xl overflow-y-auto">
                <ResearchQuestionSection
                  question={question}
                  heading="Define characteristics of the people you want to synthesize"
                />
                {validationLoading && (
                  <div className="flex flex-row gap-1 items-center w-full">
                    <span className="sr-only">Loading...</span>
                    <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="h-4 w-4 bg-primary rounded-full animate-bounce"></div>
                    <div className="text-sm text-primary p-2 rounded">
                      We are validating population traits... Please hold on for
                      a moment.
                    </div>
                  </div>
                )}
                <div className="w-full pb-4 bg-white border rounded-lg p-4 items-stretch border-primary">
                  <div className="w-100 p-4">
                    <SliderComponent
                      stateData={stateData}
                      getAgeRange={handleGetAgeRange}
                      getIncomeRange={handleGetIncomeRange}
                    ></SliderComponent>
                  </div>

                  {/* ------------Education---------------- */}
                  <div className="w-100 p-4">
                    <div className="text-md font-semibold">Education</div>
                    <div className="grid grid-cols-2 gap-4 py-2">
                      {education.map((eduValue) => (
                        <PopulationTraitValue
                          traitValue={eduValue}
                          key={eduValue}
                          traitType="education"
                          getTraitValue={getEduValue}
                        />
                      ))}
                    </div>
                  </div>

                  {/* ------------Gender---------------- */}
                  <div className="w-100 p-4">
                    <div className="text-md font-semibold">Gender</div>
                    <div className="grid grid-cols-2 gap-4 py-2">
                      {gender.map((genderValue) => (
                        <PopulationTraitValue
                          traitValue={genderValue}
                          key={genderValue}
                          traitType="gender"
                          getTraitValue={getGenderValue}
                        />
                      ))}
                    </div>
                  </div>
                  {/* ------------Number of Children---------------- */}
                  <div className="w-100 p-4">
                    <div className="text-md font-semibold">
                      Number of Children
                    </div>
                    <div className="grid grid-cols-2 gap-4 py-2">
                      {children.map((childrenValue) => {
                        return (
                          <PopulationTraitValue
                            traitValue={childrenValue}
                            key={childrenValue}
                            traitType="children"
                            getTraitValue={getChildrenValue}
                          ></PopulationTraitValue>
                        );
                      })}
                    </div>
                  </div>
                  {/* ------------Race---------------- */}
                  <div className="w-100 p-4">
                    <div className="text-md font-semibold">Racial Group</div>
                    <div className="grid grid-cols-2 gap-4 py-2">
                      {race.map((raceValue) => {
                        return (
                          <PopulationTraitValue
                            traitValue={raceValue}
                            key={raceValue}
                            traitType="race"
                            getTraitValue={getRaceValue}
                          ></PopulationTraitValue>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {/* Second View - Validation Results */}
        {currentStep === "second" && isUSA && (
          <div>
            <ResearchQuestionSection
              question={question}
              heading="Populations Validation Results"
            />
            <div className="w-full pb-4 bg-white border rounded-lg p-4 items-stretch border-primary">
              <div className="w-100 p-4">
                {validationResults && (
                  <ValidationResultsView
                    data={validationResults}
                    onDataChange={handleFinalTraits}
                  />
                )}
              </div>
            </div>
          </div>
        )}
        {/* Third View - Specialists */}
        {(currentStep === "third" || !isUSA) && (
          <div className="flex flex-col gap-6">
            <div className="flex flex-col h-full">
              <div className="flex flex-col gap-6 items-start max-w-4xl overflow-y-auto">
                <ResearchQuestionSection
                  question={question}
                  heading="Select characteristics of the people you want to synthesize"
                  subheading="Or choose a specialist to select them for you"
                />

                <div
                  id="ideation-who-guide-7"
                  className="flex flex-row gap-2 w-full justify-between"
                >
                  {SPECIALISTS.map((specialist, idx) => (
                    <SpecialistCard
                      key={idx}
                      title={specialist.title}
                      icon={specialist.icon}
                      active={activeSpecialist === specialist.title}
                      onMouseDown={() => selectSpecialist(specialist.title)}
                    />
                  ))}
                </div>
                <div className="w-full">
                  <AddNewTraitCard
                    addnewTrait={addnewTrait}
                    country={where?.name}
                    setLoadingMessage={setLoadingMessage}
                    setErrorMessage={setErrorMessage}
                    setIsLoading={setIsLoading}
                    isLoading={isLoading}
                    activateNewTrait={activateNewTrait}
                    expandedSections={expandedSections}
                    setExpandedSections={setExpandedSections}
                  />
                </div>

                {(isApiInProgress || isLoading) && (
                  <div className="flex flex-row gap-1 items-center">
                    <span className="sr-only">Loading...</span>
                    <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="h-4 w-4 bg-primary rounded-full animate-bounce"></div>
                    <div className="text-sm text-primary p-2 rounded">
                      {loadingMessage
                        ? loadingMessage
                        : "We are creating the necessary attributes and levels... Please hold on for a moment."}
                    </div>
                  </div>
                )}

                {categoryOrder.map((category) => {
                  const currentIsUSA =
                    where?.name === "United States of America (USA)";
                  const traits = groupTraitsByCategory(
                    displayTraits,
                    currentIsUSA
                  )[category];
                  const selectedCount = traits
                    ? traits.filter((trait) => trait.active).length
                    : 0;

                  return traits && traits.length > 0 ? (
                    <div
                      key={category}
                      className="w-full bg-white rounded-lg border border-[#E4E7EC] overflow-hidden"
                    >
                      <div
                        onClick={() => toggleSection(category)}
                        className="flex items-center justify-between cursor-pointer p-4 hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className="flex items-center gap-2">
                          <h3 className="text-lg font-semibold text-text-dark">
                            {
                              categoryDisplayNames[
                                category as keyof typeof categoryDisplayNames
                              ]
                            }
                          </h3>
                          <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                            {selectedCount} / {traits.length} Selected
                          </span>
                        </div>

                        {expandedSections[category] ? (
                          <ChevronUp className="w-5 h-5 text-gray-500" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-gray-500" />
                        )}
                      </div>

                      <div
                        className={`transition-all duration-300 ease-in-out ${
                          expandedSections[category]
                            ? "max-h-[2000px] opacity-100 p-4"
                            : "max-h-0 opacity-0 overflow-hidden"
                        }`}
                      >
                        <div className="grid grid-cols-2 gap-3 items-stretch w-full">
                          {traits.map((trait) => (
                            <TraitCard
                              key={trait.title}
                              title={trait.title}
                              values={trait.values}
                              active={trait.active}
                              setSelected={() =>
                                toggleSelectedTrait(trait.title)
                              }
                              onDelete={
                                category === "customTraits"
                                  ? () => {
                                      // Remove this trait from displayTraits
                                      setDisplayTraits(
                                        displayTraits.filter(
                                          (t) => t.title !== trait.title
                                        )
                                      );
                                    }
                                  : undefined
                              }
                              isCustom={category === "customTraits"}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
          </div>
        )}
        {getNumSelected() === 0 && currentStep === "third" && !isUSA && (
          <div className="sticky bottom-[60px] mt-4 flex gap-2 text-error-red items-center bg-red-100 p-3 rounded-md">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TriangleAlert className="h-5 w-5 text-red-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  No traits selected. Please select at least one trait to
                  continue.
                </h3>
              </div>
            </div>
          </div>
        )}

        {getErrorMessage() && (
          <div className="sticky bottom-[60px] flex gap-2 text-error-red items-center bg-red-100 p-3 rounded-md">
            <div className="flex items-start">
              <div className="flex-shrink-0 mt-0.5">
                <TriangleAlert className="h-5 w-5 text-red-500" />
              </div>
              <div className="ml-3">
                <h3
                  className="text-sm font-medium text-red-800"
                  dangerouslySetInnerHTML={{
                    __html: getErrorMessage()?.message || "",
                  }}
                ></h3>

                {getErrorMessage()?.showTryAgain && (
                  <div className="mt-1 text-sm text-red-700">
                    Please adjust the traits and{" "}
                    <button
                      type="button"
                      onClick={handleValidation}
                      className="text-sm font-medium text-red-800 hover:text-red-600 focus:outline-none"
                    >
                      Try Again
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        <div className="sticky bottom-0 flex w-full justify-between bg-background py-2">
          <button
            className="font-inter text-base font-medium py-2.5 px-6 bg-white hover:bg-secondary-grey border border-card-border shadow-sm rounded-lg"
            onClick={handleBack}
          >
            Back
          </button>
          <button
            className={`font-inter text-base font-semibold py-2.5 ${
              validationLoading ||
              (currentStep === "third" && continueDisabled())
                ? "bg-[#ECEDFB] cursor-not-allowed"
                : "bg-primary hover:bg-primary-dark"
            } text-white rounded-lg shadow-sm w-[170px] flex items-center justify-center gap-2`}
            onClick={handleContinue}
            disabled={
              validationLoading ||
              (currentStep === "third" && continueDisabled())
            }
          >
            Continue
          </button>
        </div>
      </div>
    </>
  );
};

export default WhoComponent;
