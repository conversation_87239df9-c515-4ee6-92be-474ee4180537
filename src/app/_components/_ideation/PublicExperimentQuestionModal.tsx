/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
import { Fragment, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { ModalClosureXIcon } from "../../../../public/icons/CausalCarouselIcons";
import GlobeImage from "../../../../public/icons/WhenWhereIcons";
import { FolderLock, FolderOpen } from "lucide-react";

interface PublicExperimentQuestionModalProps {
  showModal: boolean;
  setShowModal: (show: boolean) => void;
  onModalClose: () => void; // Add this line
  setExperimentPrivate: () => void;
  setExperimentPublic: () => void;
}

const PublicExperimentQuestionModal = ({
  setExperimentPrivate,
  setExperimentPublic,
  showModal,
  setShowModal,
  onModalClose,
}: PublicExperimentQuestionModalProps) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  const handleSubmit = (isPrivateSetting: boolean) => {
    if (isPrivateSetting) {
      setExperimentPrivate();
    } else {
      setExperimentPublic();
    }
    onModalClose();
    setShowModal(false);
  };

  const handleClose = () => {
    setShowModal(false);
  };

  return (
    <Transition.Root show={showModal} as={Fragment}>
      <Dialog
        as="div"
        static
        className="fixed z-10 inset-0 overflow-y-auto"
        onClose={handleClose}
      >
        <div
          className="flex min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Dialog.Overlay className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <span
            className="hidden sm:inline-block sm:align-middle sm:h-screen"
            aria-hidden="true"
          >
            &#8203;
          </span>

          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enterTo="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <div className="inline-block align-bottom bg-white text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-[620px] h-auto rounded-[12px] p-8 relative">
              <button
                onMouseDown={handleClose}
                className="absolute top-0 right-0 m-5"
              >
                <ModalClosureXIcon />
              </button>
              <div>
                <div className="text-xl font-semibold mb-5">
                  Do you want to keep your experiments public?
                </div>
                <GlobeImage />
                <div style={{ fontSize: "18px" }} className="mt-5 mb-5">
                  By making your experiment public, you contribute to a global
                  understanding of human behavior. Every shared insight is a
                  step towards collective advancement.
                </div>
                <div style={{ fontSize: "18px" }} className="mb-5">
                  You will be able to edit the privacy of your experiments later
                  in the Experiments page.
                </div>
              </div>
              <div className="flex justify-between">
                <button
                  className={`flex flex-row gap-2 justify-center font-inter text-base font-semibold py-2.5 bg-white hover:bg-secondary-grey text-dark border border-dark rounded-lg shadow-sm w-[276px] h-[44px] mr-5`}
                  onMouseDown={() => handleSubmit(true)}
                >
                  <FolderLock strokeWidth={1} />
                  <span id="back-question">{"Make private"}</span>
                </button>
                <button
                  className="flex flex-row justify-center gap-2 font-inter text-base font-semibold py-2.5 bg-primary hover:bg-primary-dark text-white rounded-lg shadow-sm w-[276px] h-[44px]"
                  tabIndex={1}
                  onMouseDown={() => handleSubmit(false)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleSubmit(false);
                    }
                  }}
                >
                  <FolderOpen strokeWidth={1} />
                  <span id="continue-question">{"Keep public"}</span>
                </button>
              </div>
            </div>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default PublicExperimentQuestionModal;
