import React, { useContext, useEffect, useState } from "react";
import * as Slider from "@radix-ui/react-slider";
import { Input } from "@/components/ui/input";
import { StateData } from "./objects";
import ExperimentCreationContext from "./ExperimentCreationContext";

interface SliderComponentProps {
  stateData: StateData;
  getAgeRange: (ageRange: number[]) => void;
  getIncomeRange: (incomeRange: number[]) => void;
}

const SliderComponent: React.FC<SliderComponentProps> = ({
  stateData,
  getAgeRange,
  getIncomeRange,
}) => {
  const { selectedState } = useContext(ExperimentCreationContext);

  // Initialize from localStorage if values exist, otherwise use state default values
  const initAgeRange = () => {
    const storedAge = localStorage.getItem("age");
    if (storedAge) {
      return JSON.parse(storedAge);
    }
    return [stateData.Age.min, stateData.Age.max];
  };

  const initIncomeRange = () => {
    const storedIncome = localStorage.getItem("income");
    if (storedIncome) {
      return JSON.parse(storedIncome);
    }
    return [
      stateData["Household income"].min,
      stateData["Household income"]["95th_percentile"],
    ];
  };

  // Numeric slider state (used by the Radix Slider)
  const [ageRangeSelect, setAgeRangeSelect] =
    useState<number[]>(initAgeRange());
  const [incomeRangeSelect, setIncomeRangeSelect] =
    useState<number[]>(initIncomeRange());

  // Input field states (as strings so the user can type freely)
  const [ageInputMin, setAgeInputMin] = useState<string>(
    ageRangeSelect[0].toString()
  );
  const [ageInputMax, setAgeInputMax] = useState<string>(
    ageRangeSelect[1].toString()
  );
  const [incomeInputMin, setIncomeInputMin] = useState<string>(
    incomeRangeSelect[0].toString()
  );
  const [incomeInputMax, setIncomeInputMax] = useState<string>(
    incomeRangeSelect[1].toString()
  );

  const [ageError, setAgeError] = useState<string | null>(null);
  const [incomeError, setIncomeError] = useState<string | null>(null);

  // Only update from stateData when the selected state changes
  useEffect(() => {
    const previousState = localStorage.getItem("previousSelectedState");

    // Only reset values if the state has changed
    if (previousState !== selectedState) {
      // Reset to the new state's default values
      setAgeRangeSelect([stateData.Age.min, stateData.Age.max]);
      setIncomeRangeSelect([
        stateData["Household income"].min,
        stateData["Household income"]["95th_percentile"],
      ]);

      setAgeInputMin(stateData.Age.min.toString());
      setAgeInputMax(stateData.Age.max.toString());
      setIncomeInputMin(stateData["Household income"].min.toString());
      setIncomeInputMax(
        stateData["Household income"]["95th_percentile"].toString()
      );

      // Save the new state to localStorage
      localStorage.setItem("previousSelectedState", selectedState || "");

      // Remove old saved values
      localStorage.removeItem("age");
      localStorage.removeItem("income");
    }
  }, [selectedState, stateData]);

  const validateAgeRange = (
    value: number[],
    min: number,
    max: number
  ): string | null => {
    if (value[0] < min || value[1] > max) {
      return `Values must be between ${min} and ${max}.`;
    }
    if (value[0] > value[1]) {
      return "Min value cannot be greater than max value.";
    }
    return null;
  };

  const validateIncomeRange = (
    value: number[],
    min: number,
    percentile95: number
  ): string | null => {
    if (value[0] < min || value[1] > percentile95) {
      return `Values must be between ${min} and ${percentile95}.`;
    }
    if (value[0] > value[1]) {
      return "Min value cannot be greater than max value.";
    }
    return null;
  };

  // Update age values from input fields immediately if both values are valid.
  const updateAgeFromInputs = (minStr: string, maxStr: string) => {
    if (minStr === "" || maxStr === "") return;
    const minVal = parseInt(minStr, 10);
    const maxVal = parseInt(maxStr, 10);
    if (isNaN(minVal) || isNaN(maxVal)) {
      setAgeError("Please enter valid numbers.");
      return;
    }
    const error = validateAgeRange(
      [minVal, maxVal],
      stateData.Age.min,
      stateData.Age.max
    );
    setAgeError(error);
    if (!error) {
      setAgeRangeSelect([minVal, maxVal]);
      localStorage.setItem("age", JSON.stringify([minVal, maxVal]));
    }
  };

  // Update income values from input fields immediately if both values are valid.
  const updateIncomeFromInputs = (minStr: string, maxStr: string) => {
    if (minStr === "" || maxStr === "") return;
    const minVal = parseInt(minStr, 10);
    const maxVal = parseInt(maxStr, 10);
    if (isNaN(minVal) || isNaN(maxVal)) {
      setIncomeError("Please enter valid numbers.");
      return;
    }
    const error = validateIncomeRange(
      [minVal, maxVal],
      stateData["Household income"].min,
      stateData["Household income"]["95th_percentile"]
    );
    setIncomeError(error);
    if (!error) {
      setIncomeRangeSelect([minVal, maxVal]);
      localStorage.setItem("income", JSON.stringify([minVal, maxVal]));
    }
  };

  // Input change handlers for Age fields.
  const handleAgeMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVal = e.target.value;
    setAgeInputMin(newVal);
    updateAgeFromInputs(newVal, ageInputMax);
  };

  const handleAgeMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVal = e.target.value;
    setAgeInputMax(newVal);
    updateAgeFromInputs(ageInputMin, newVal);
  };

  // Input change handlers for Income fields.
  const handleIncomeMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cursorPosition = e.target.selectionStart || 0;
    const rawValue = e.target.value.replace(/,/g, "");
    setIncomeInputMin(rawValue);
    updateIncomeFromInputs(rawValue, incomeInputMax);
    requestAnimationFrame(() => {
      e.target.setSelectionRange(cursorPosition, cursorPosition);
    });
  };

  const handleIncomeMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cursorPosition = e.target.selectionStart || 0;
    const rawValue = e.target.value.replace(/,/g, "");
    setIncomeInputMax(rawValue);
    updateIncomeFromInputs(incomeInputMin, rawValue);
    requestAnimationFrame(() => {
      e.target.setSelectionRange(cursorPosition, cursorPosition);
    });
  };

  // When the slider for Age changes, update both the slider and input states.
  const handleAgeSelectChange = (newValue: number[]) => {
    const error = validateAgeRange(
      newValue,
      stateData.Age.min,
      stateData.Age.max
    );
    setAgeError(error);
    if (!error) {
      setAgeRangeSelect(newValue);
      setAgeInputMin(newValue[0].toString());
      setAgeInputMax(newValue[1].toString());
      localStorage.setItem("age", JSON.stringify(newValue));
    }
  };

  // When the slider for Income changes, update both the slider and input states.
  const handleIncomeSelectChange = (newValue: number[]) => {
    const error = validateIncomeRange(
      newValue,
      stateData["Household income"].min,
      stateData["Household income"]["95th_percentile"]
    );
    setIncomeError(error);
    if (!error) {
      setIncomeRangeSelect(newValue);
      setIncomeInputMin(newValue[0].toString());
      setIncomeInputMax(newValue[1].toString());
      localStorage.setItem("income", JSON.stringify(newValue));
    }
  };

  useEffect(() => {
    getAgeRange(ageRangeSelect);
    getIncomeRange(incomeRangeSelect);
  }, [ageRangeSelect, incomeRangeSelect, getAgeRange, getIncomeRange]);

  return (
    <div>
      {/* Age Slider */}
      <div>
        <div className="text-md font-semibold">Age</div>
        <div className="flex justify-between items-end">
          <div className="w-24">
            <div className="py-2">Min</div>
            <Input
              type="number"
              value={ageInputMin}
              onChange={handleAgeMinChange}
              className="w-[110px]"
            />
          </div>
          <div className="w-100">
            <Slider.Root
              className="relative flex items-center select-none touch-none w-[300px] h-5"
              value={ageRangeSelect}
              onValueChange={handleAgeSelectChange}
              min={stateData.Age.min}
              max={stateData.Age.max}
              step={1}
            >
              <Slider.Track className="bg-slate-200 relative grow rounded-full h-[8px]">
                <Slider.Range className="absolute bg-[#312E81] rounded-full h-full" />
              </Slider.Track>
              <Slider.Thumb
                className="block w-5 h-5 bg-white border-2 border-[#312E81] rounded-full hover:bg-slate-100 focus:outline-none"
                aria-label="Min age"
              />
              <Slider.Thumb
                className="block w-5 h-5 bg-white border-2 border-[#312E81] rounded-full hover:bg-slate-100 focus:outline-none"
                aria-label="Max age"
              />
            </Slider.Root>
          </div>
          <div className="w-24">
            <div className="py-2">Max</div>
            <Input
              type="number"
              value={ageInputMax}
              onChange={handleAgeMaxChange}
              className="w-[110px]"
            />
          </div>
        </div>
        {ageError && (
          <div className="text-red-500 text-sm mt-3">{ageError}</div>
        )}
      </div>
      {/* Income Slider */}
      <div className="pt-10">
        <div className="text-md font-semibold">Income</div>
        <div className="flex justify-between items-end">
          <div className="w-24">
            <div className="py-2">Min</div>
            <div className="relative">
              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500">
                $
              </span>
              <Input
                type="text"
                value={incomeInputMin.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
                onChange={handleIncomeMinChange}
                className="pl-6 w-[110px]"
              />
            </div>
          </div>
          <div className="w-100">
            <Slider.Root
              className="relative flex items-center select-none touch-none w-[300px] h-5"
              value={incomeRangeSelect}
              onValueChange={handleIncomeSelectChange}
              min={stateData["Household income"].min}
              max={stateData["Household income"]["95th_percentile"]}
              step={1000}
            >
              <Slider.Track className="bg-slate-200 relative grow rounded-full h-[8px]">
                <Slider.Range className="absolute bg-[#312E81] rounded-full h-full" />
              </Slider.Track>
              <Slider.Thumb
                className="block w-5 h-5 bg-white border-2 border-[#312E81] rounded-full hover:bg-slate-100 focus:outline-none"
                aria-label="Min income"
              />
              <Slider.Thumb
                className="block w-5 h-5 bg-white border-2 border-[#312E81] rounded-full hover:bg-slate-100 focus:outline-none"
                aria-label="Max income"
              />
            </Slider.Root>
          </div>
          <div className="w-24">
            <div className="py-2">Max</div>
            <div className="relative">
              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500">
                $
              </span>
              <Input
                type="text"
                value={incomeInputMax.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
                onChange={handleIncomeMaxChange}
                className="pl-6 w-[110px]"
              />
            </div>
          </div>
        </div>
        {incomeError && (
          <div className="text-red-500 text-sm mt-3">{incomeError}</div>
        )}
      </div>
    </div>
  );
};

export default SliderComponent;
