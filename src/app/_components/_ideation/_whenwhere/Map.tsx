import { useEffect, useState } from "react";
import LocationMarker from "./LocationMarker";

interface MapProps {
  selected: boolean;
  x?: number;
  y?: number;
}

const Map = ({ selected, x, y }: MapProps) => {
  const [placeLocationMarker, setPlaceLocationMarker] = useState(selected);

  useEffect(() => {
    setPlaceLocationMarker(selected);
  }, [selected]);

  return (
    <div className="relative h-[260px] w-[800px] bg-cover bg-center bg-no-repeat bg-map">
      {placeLocationMarker && <LocationMarker x={x} y={y} />}
    </div>
  );
};

export default Map;
