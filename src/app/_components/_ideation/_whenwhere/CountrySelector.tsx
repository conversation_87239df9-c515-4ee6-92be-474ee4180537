/* eslint-disable prettier/prettier */
import { Menu, Transition } from "@headlessui/react";
import { Fragment } from "react";
import Image from "next/image";
import { Country } from "../objects";
import Tooltip from "../../_util/ToolTip";
import { Info, ChevronDown } from "lucide-react";

interface CountrySelectorProps {
  countries: Country[];
  selectedCountry?: Country;
  // eslint-disable-next-line no-unused-vars
  setSelectedCountry: (c: Country) => void;
}

const CountrySelector = ({
  countries,
  selectedCountry,
  setSelectedCountry,
}: CountrySelectorProps) => {
  return (
    <Menu as="div" className="relative inline-block text-left">
      <div className="flex flex-col items-stretch gap-1.5">
        <div className="flex gap-2 items-center">
          <div
            id="ideation-when-guide-9"
            className="group relative flex gap-2 font-medium text-text-placeholder text-sm py-1 justify-start items-center cursor-pointer"
          >
            Where
            <Info />
            <Tooltip
              message="Specify the country where your experiment will take place. Use the dropdown to select."
              position="right"
            />
          </div>
        </div>

        <Menu.Button className="w-full bg-white px-3.5 py-2.5 font-inter placeholder-text-text-placeholder text-base rounded-xl border border-[#D0D5DD] focus:border-2 focus:border-primary focus:outline-none focus:ring-0)">
          <div className="flex flex-row justify-center items-center gap-2">
            {selectedCountry !== undefined && (
              <Image
                src={selectedCountry.flag}
                height={24}
                width={24}
                alt="Country flag"
              />
            )}
            <p className="text-base text-text-dark font-roboto flex-grow text-start">
              {selectedCountry === undefined
                ? "Select a country"
                : selectedCountry.name}
            </p>
            <ChevronDown className="w-5 h-5" />
          </div>
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-full h-56 overflow-y-scroll origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          {countries.map((country, idx) => {
            return (
              <Menu.Item key={idx}>
                <div
                  className="flex flex-row gap-2 px-3 py-2 hover:cursor-pointer"
                  onMouseDown={() => setSelectedCountry(country)}
                >
                  <Image
                    src={country.flag}
                    height={24}
                    width={24}
                    alt="Country flag"
                  />
                  <p>{country.name}</p>
                </div>
              </Menu.Item>
            );
          })}
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default CountrySelector;
