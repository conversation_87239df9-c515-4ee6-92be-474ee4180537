"use client";
import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useUser } from "@auth0/nextjs-auth0/client";
import { useAuth0Token } from "../_util/Auth0TokenContext";
import * as Sentry from "@sentry/react";

interface SubscriptionContextType {
  stripeCustomerId: string | null;
  renewalDate: string | null;
  subscriptionStatus: string | null;
  roles: string[];
  isLoading: boolean;
  error: string | null;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(
  undefined
);

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider = ({
  children,
}: SubscriptionProviderProps) => {
  const { user } = useUser();
  const { auth0AccessToken } = useAuth0Token();
  const [stripeCustomerId, setStripeCustomerId] = useState<string | null>(null);
  const [renewalDate, setRenewalDate] = useState<string | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<string | null>(
    null
  );
  const [roles, setRoles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user && auth0AccessToken) {
      fetch(
        `${process.env.NEXT_PUBLIC_AUTH0_AUDIENCE}users?q=user_id:"${user.sub}"&search_engine=v3`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${auth0AccessToken}`,
          },
        }
      )
        .then((response) => response.json())
        .then((data) => {
          const userData = data[0];
          const { app_metadata } = userData;

          if (app_metadata) {
            if (app_metadata.stripe_customer_id) {
              setStripeCustomerId(app_metadata.stripe_customer_id);
              checkSubscriptionStatus(
                app_metadata.stripe_customer_id,
                auth0AccessToken
              );
            }
            if (app_metadata.roles) {
              setRoles(app_metadata.roles);
            }
          }
          setIsLoading(false);
        })
        .catch((error) => {
          console.error("Error fetching user data:", error);
          Sentry.captureException(error);
          setError("Failed to fetch user data");
          setIsLoading(false);
        });
    } else {
      setIsLoading(false);
    }
  }, [user, auth0AccessToken]);

  const checkSubscriptionStatus = async (customerId: string, token: string) => {
    try {
      // Check if the backend endpoint is defined
      if (!process.env.NEXT_PUBLIC_BACKEND_ENDPOINT) {
        console.warn("NEXT_PUBLIC_BACKEND_ENDPOINT is not defined, skipping subscription check");
        setSubscriptionStatus("Unknown");
        return;
      }
      
      const endpoint = `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/check-subscription/?customer_id=${customerId}`;
      console.log("Checking subscription status at:", endpoint);
      
      const response = await fetch(endpoint, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorMessage = await response.text();
        console.warn("Subscription check failed:", errorMessage);
        
        // Set a default status instead of showing an error
        setSubscriptionStatus("Unknown");
        
        // Only log the error, don't set it in the state to avoid UI disruption
        if (errorMessage.includes("Not Found")) {
          console.info("Subscription endpoint not available, this is expected in development");
        } else {
          Sentry.captureException(new Error(`Subscription check failed: ${errorMessage}`));
        }
        return;
      }

      const data = await response.json();
      setSubscriptionStatus(
        data.subscription_status.status === "active" ||
          data.subscription_status.status === "trialing"
          ? "Active"
          : "Not Subscribed"
      );
      const renewalDateInEpoch = data.subscription_status.current_period_end;
      const renewalDate = new Date(renewalDateInEpoch * 1000);

      const formattedRenewalDate = renewalDateInEpoch
        ? renewalDate.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })
        : null;
      setRenewalDate(formattedRenewalDate);
    } catch (error) {
      console.warn("Error checking subscription:", error);
      
      // Set a default status instead of showing an error
      setSubscriptionStatus("Unknown");
      
      // Only capture serious errors
      if (error instanceof Error && !error.message.includes("Failed to fetch")) {
        Sentry.captureException(error);
      }
    }
  };

  return (
    <SubscriptionContext.Provider
      value={{
        stripeCustomerId,
        renewalDate,
        subscriptionStatus,
        roles,
        isLoading,
        error,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error(
      "useSubscription must be used within a SubscriptionProvider"
    );
  }
  return context;
};
