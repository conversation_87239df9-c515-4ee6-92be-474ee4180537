/* Chat interface scrolling fixes */

/* Ensure the chat container takes full height and scrolls properly */
.chat-container {
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* Ensure the messages container scrolls properly */
.messages-container {
  flex: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
  padding-bottom: 80px; /* Space for the input */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Fix for iOS momentum scrolling */
.ios-momentum-scroll {
  -webkit-overflow-scrolling: touch;
}

/* Prevent content from being hidden behind fixed elements */
.content-padding-bottom {
  padding-bottom: 80px;
}

/* Ensure fixed elements stay in place */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

/* Prevent text from overflowing in chat messages */
.chat-message-content {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Improve scrollbar appearance */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.8);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.5);
}

/* Fix for Firefox scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.3) rgba(241, 245, 249, 0.8);
}

/* Fix for mobile viewport height issues */
@supports (-webkit-touch-callout: none) {
  .chat-container {
    height: -webkit-fill-available;
  }
}

/* Fix for nested scrollable areas */
.nested-scroll-container {
  max-height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.nested-scroll-content {
  flex: 1;
  overflow-y: auto;
}
