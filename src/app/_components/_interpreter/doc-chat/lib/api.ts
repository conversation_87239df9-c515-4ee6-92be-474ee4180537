import {
  RunIdRequest,
  ArtifactResponse,
  ReportRequest,
  TextReportResponse,
  ChatRequest,
  ChatResponse,
  ChatMessage,
  AMCEData,
  MindsetItem,
  Feature,
} from "@/lib/api-types";
import { apiRequest } from "@/lib/queryClient";

/**
 * Fetch experiment details by run_id
 */
export async function fetchExperimentDetails(runId: string): Promise<any> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/get-experiment-details`,
      { run_id: runId }
    );

    return await response.json();
  } catch (error) {
    console.error("Error fetching experiment details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Fetch experiment artifacts by run_id
 */
export async function fetchExperimentArtifacts(
  runId: string
): Promise<ArtifactResponse> {
  try {
    // Use the Next.js API endpoint
    const response = await fetch(`/api/get-experiment-artifact`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ run_id: runId }),
      credentials: "include",
    });

    // Check if response is OK
    if (!response.ok) {
      const text = await response.text();
      throw new Error(`${response.status}: ${text}`);
    }

    const data = await response.json();

    // Check if the API returned success: false
    if (!data.success) {
      console.warn("API returned success: false for experiment artifacts", data.error);
      // Return a minimal valid response with the error
      return {
        success: false,
        experiment_definition_file: "",
        analytics_output_file: "",
        experiment_definition_data: {
          title: `Experiment ${runId}`,
          context: "No experiment context available",
          target_behavior: "Unknown target behavior",
          country: "Unknown",
          year: new Date().getFullYear(),
        },
        error: data.error || "API returned unsuccessful response",
      };
    }

    return data;
  } catch (error) {
    console.error("Error fetching experiment artifacts:", error);
    // Return a fallback response with minimal valid data
    return {
      success: false,
      experiment_definition_file: "",
      analytics_output_file: "",
      experiment_definition_data: {
        title: `Experiment ${runId}`,
        context: "No experiment context available",
        target_behavior: "Unknown target behavior",
        country: "Unknown",
        year: new Date().getFullYear(),
      },
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Fetch a single artifact file by filename
 */
export async function fetchSingleArtifact(fileName: string): Promise<any> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/get-single-artifact`,
      { file_name: fileName }
    );

    return await response.json();
  } catch (error) {
    console.error("Error fetching single artifact:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Fetch processed experiment details
 */
export async function fetchProcessedExperimentDetails(
  runId: string
): Promise<any> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/get-processed-experiment-details`,
      { run_id: runId }
    );

    return await response.json();
  } catch (error) {
    console.error("Error fetching processed experiment details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Fetch processed AMCE data
 */
export async function fetchProcessedAMCEData(
  runId: string
): Promise<{ success: boolean; data?: AMCEData; error?: string }> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/get-processed-amce-data`,
      { run_id: runId }
    );

    return await response.json();
  } catch (error) {
    console.error("Error fetching processed AMCE data:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Fetch processed mindset data
 */
export async function fetchProcessedMindsetData(
  runId: string
): Promise<{ success: boolean; data?: MindsetItem[]; error?: string }> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/get-processed-mindset-data`,
      { run_id: runId }
    );

    return await response.json();
  } catch (error) {
    console.error("Error fetching processed mindset data:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Generate experiment summary
 */
export async function generateExperimentSummary(runId: string): Promise<any> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/generate-experiment-summary`,
      { run_id: runId }
    );

    return await response.json();
  } catch (error) {
    console.error("Error generating experiment summary:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Generate causal sentences (AMCE)
 */
export async function generateCausalSentences(runId: string): Promise<any> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/generate-causal-sentences`,
      { run_id: runId }
    );

    return await response.json();
  } catch (error) {
    console.error("Error generating causal sentences:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Generate mindset sentences
 */
export async function generateMindsetSentences(runId: string): Promise<any> {
  try {
    // Use the Next.js API endpoint
    const response = await apiRequest(
      "POST",
      `/api/generate-mindset-sentences`,
      { run_id: runId }
    );

    return await response.json();
  } catch (error) {
    console.error("Error generating mindset sentences:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Generate a causal insights report
 */
export async function generateReport(
  runId: string
): Promise<TextReportResponse> {
  try {
    // Use the Next.js API endpoint
    const response = await fetch(`/api/generate-report`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        run_id: runId,
        format: "text",
      }),
      credentials: "include",
    });

    // Check if response is OK
    if (!response.ok) {
      const text = await response.text();
      throw new Error(`${response.status}: ${text}`);
    }

    // Check the content type to determine how to handle the response
    const contentType = response.headers.get('content-type') || '';

    if (contentType.includes('application/pdf')) {
      // Handle PDF response
      console.log('Received PDF response from report API');
      const blob = await response.blob();
      const pdfUrl = URL.createObjectURL(blob);

      // Return a success response with a link to the PDF
      return {
        success: true,
        report_text: "A report has been generated for this experiment.",
        pdfUrl: pdfUrl,
        error: undefined
      };
    } else if (contentType.includes('application/json')) {
      // Handle JSON response
      const jsonData = await response.json();
      return jsonData;
    } else {
      // Handle text or other response types
      const text = await response.text();
      if (text.startsWith('%PDF')) {
        // This is a PDF file with incorrect content type
        console.log('Received PDF content with incorrect content type');

        // Convert the text to a Uint8Array of bytes
        const encoder = new TextEncoder();
        const pdfBytes = encoder.encode(text);

        // Create a blob from the bytes
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const pdfUrl = URL.createObjectURL(blob);

        return {
          success: true,
          report_text: text, // Keep the original PDF content for further processing
          pdfUrl: pdfUrl,
          error: undefined
        };
      }

      // Try to parse as JSON
      try {
        return JSON.parse(text);
      } catch (e) {
        // Return as plain text if not JSON
        return {
          success: true,
          report_text: text,
          error: undefined
        };
      }
    }
  } catch (error) {
    console.error("Error generating report:", error);
    return {
      success: false,
      report_text: "",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Send a message to the chat API with experiment context
 * This implementation uses only the specified external APIs for the chat feature
 */
export async function sendChatMessage(
  runId: string,
  messages: ChatMessage[]
): Promise<ChatResponse> {
  try {
    // Get the last user message
    const lastUserMessage = messages[messages.length - 1];
    const userQuery = lastUserMessage.content;

    // Fetch data from all the required external APIs in parallel
    const [
      experimentDetails,
      experimentArtifacts,
      processedExperimentDetails,
      processedAMCEData,
      processedMindsetData,
      causalSentences,
      experimentSummary,
      mindsetSentences
    ] = await Promise.all([
      fetchExperimentDetails(runId),
      fetchExperimentArtifacts(runId),
      fetchProcessedExperimentDetails(runId),
      fetchProcessedAMCEData(runId),
      fetchProcessedMindsetData(runId),
      generateCausalSentences(runId),
      generateExperimentSummary(runId),
      generateMindsetSentences(runId)
    ]);

    // Combine all the data to create a comprehensive context (for debugging)
    console.log("Chat context data loaded:", {
      experimentDetails: !!experimentDetails,
      experimentArtifacts: !!experimentArtifacts,
      processedExperimentDetails: !!processedExperimentDetails,
      processedAMCEData: !!processedAMCEData,
      processedMindsetData: !!processedMindsetData,
      causalSentences: !!causalSentences,
      experimentSummary: !!experimentSummary,
      mindsetSentences: !!mindsetSentences
    });

    // Generate a response based on the user query and the context
    let responseContent = "";

    // Check if the query is about experiment details
    if (userQuery.toLowerCase().includes("experiment") ||
        userQuery.toLowerCase().includes("details") ||
        userQuery.toLowerCase().includes("what is this experiment about")) {
      responseContent = `This experiment is titled "${experimentDetails?.title || 'Untitled Experiment'}". ${experimentSummary?.summary || ''}`;
    }
    // Check if the query is about causal relationships
    else if (userQuery.toLowerCase().includes("causal") ||
             userQuery.toLowerCase().includes("relationship") ||
             userQuery.toLowerCase().includes("effect") ||
             userQuery.toLowerCase().includes("impact")) {
      responseContent = causalSentences?.sentences?.join("\n\n") ||
        "I couldn't find specific causal relationships in this experiment.";
    }
    // Check if the query is about mindsets or segments
    else if (userQuery.toLowerCase().includes("mindset") ||
             userQuery.toLowerCase().includes("segment") ||
             userQuery.toLowerCase().includes("group") ||
             userQuery.toLowerCase().includes("behavior") ||
             userQuery.toLowerCase().includes("behaviour") ||
             userQuery.toLowerCase().includes("consumer type") ||
             userQuery.toLowerCase().includes("customer segment")) {

      // First check if we have mindset data
      if (processedMindsetData?.success && processedMindsetData?.data && processedMindsetData.data.length > 0) {
        // Group mindset data by mindset name
        const mindsetGroups: Record<string, MindsetItem[]> = {};
        processedMindsetData.data.forEach(item => {
          const mindset = item.mindset || "Unknown Mindset";
          if (!mindsetGroups[mindset]) {
            mindsetGroups[mindset] = [];
          }
          mindsetGroups[mindset].push(item);
        });

        // Format the mindset data
        responseContent = "Here's information about the mindset segments in this experiment:\n\n";

        // Add each mindset group
        Object.entries(mindsetGroups).forEach(([mindset, items]: [string, MindsetItem[]]) => {
          // Extract percentage if available in the mindset name
          const percentageMatch = mindset.match(/(\d+\.?\d*)%/);
          const percentage = percentageMatch ? percentageMatch[1] : '';

          responseContent += `## ${mindset}\n\n`;

          // Add a brief description of the mindset
          if (percentage) {
            responseContent += `This segment represents approximately ${percentage}% of the population.\n\n`;
          }

          // Add top preferences for this mindset (limit to 5 for readability)
          responseContent += "Key preferences for this mindset:\n";

          // Sort items by AMCE value (absolute) to show strongest effects first
          const sortedItems = [...items].sort((a, b) => {
            return Math.abs(b.AMCE || 0) - Math.abs(a.AMCE || 0);
          }).slice(0, 5);

          sortedItems.forEach(item => {
            const effect = item.AMCE > 0 ? "increased" : "decreased";
            const amceValue = Math.abs(item.AMCE * 100).toFixed(1);
            responseContent += `- ${item.attribute_text || "Unknown Attribute"}: ${item.level_text || "Unknown Level"} ${effect} preference by ${amceValue}%\n`;
          });

          responseContent += "\n";
        });
      }
      // Then check if we have mindset sentences as a fallback
      else if (mindsetSentences?.sentences && mindsetSentences.sentences.length > 0) {
        responseContent = mindsetSentences.sentences.join("\n\n");
      }
      // If no mindset data is available
      else {
        responseContent = "I couldn't find specific mindset information in this experiment.";
      }
    }
    // Check if the query is about AMCE data
    else if (userQuery.toLowerCase().includes("amce") ||
             userQuery.toLowerCase().includes("data") ||
             userQuery.toLowerCase().includes("results") ||
             userQuery.toLowerCase().includes("effect")) {

      console.log("AMCE data:", processedAMCEData);

      // First check if we have AMCE data in the expected format
      if (processedAMCEData?.data?.Features && processedAMCEData.data.Features.length > 0) {
        const topFeatures = processedAMCEData.data.Features.slice(0, 5);
        responseContent = "Here are the top findings from the AMCE analysis:\n\n" +
          topFeatures.map(feature => `- ${feature.attribute}: ${(feature.amce * 100).toFixed(2)}% effect`).join("\n");
      }
      // Check if we have AMCE data in a different format
      else if (processedAMCEData?.data && Array.isArray(processedAMCEData.data) && processedAMCEData.data.length > 0) {
        // Sort by absolute AMCE value
        const sortedData = [...processedAMCEData.data].sort((a, b) => {
          return Math.abs(b.AMCE || 0) - Math.abs(a.AMCE || 0);
        });

        // Take top 10 effects
        const topEffects = sortedData.slice(0, 10);

        responseContent = "Here are the key AMCE effects from the analysis:\n\n";

        topEffects.forEach(item => {
          const effect = item.AMCE > 0 ? "increased" : "decreased";
          const amceValue = Math.abs(item.AMCE * 100).toFixed(1);
          responseContent += `- ${item.attribute_text || item.attribute || "Unknown Attribute"}: ${item.level_text || item.level || "Unknown Level"} ${effect} preference by ${amceValue}%\n`;
        });
      }
      // Check if we have causal sentences as a fallback
      else if (causalSentences?.sentences && causalSentences.sentences.length > 0) {
        responseContent = "Here are the key causal effects from the analysis:\n\n" +
          causalSentences.sentences.join("\n\n");
      }
      // If no AMCE data is available
      else {
        responseContent = "I couldn't find specific AMCE data for this experiment.";
      }
    }
    // Default response if no specific category is matched
    else {
      responseContent = `I've analyzed the experiment "${experimentDetails?.title || 'Untitled Experiment'}".\n\n${experimentSummary?.summary || ''}\n\nYou can ask me about specific details, causal relationships, mindset segments, or AMCE data.`;
    }

    return {
      success: true,
      message: {
        role: "assistant",
        content: responseContent
      }
    };
  } catch (error) {
    console.error("Error processing chat with external APIs:", error);
    return {
      success: false,
      message: {
        role: "assistant",
        content: `I'm sorry, I encountered an error processing your request. ${
          error instanceof Error ? error.message : "Please try again."
        }`
      },
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Test API connection
 */
export async function testConnection(): Promise<{
  status: string;
  message: string;
}> {
  try {
    const response = await fetch("/api/test-connection", {
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`API returned status ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Connection test failed:", error);
    return {
      status: "error",
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
