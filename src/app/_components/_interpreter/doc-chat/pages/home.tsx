import React, { useState } from "react";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, ArrowRight } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";

export default function Home() {
  // Default sample run ID for easy testing
  const [runId, setRunId] = useState("70f56ad3-ef51-417c-bca3-00747fb15e66");
  const [, navigate] = useLocation();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (runId.trim()) {
      navigate(`/experiment/${encodeURIComponent(runId.trim())}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white flex items-center justify-center p-4">
      <div className="w-full max-w-5xl mx-auto">
        <div className="grid gap-8 md:grid-cols-2">
          {/* Left Section */}
          <div className="flex flex-col justify-center space-y-4">
            <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-blue-50 text-blue-700 text-sm font-medium w-fit">
              <Bot size={16} />
              <span>AI-Powered Analysis</span>
            </div>

            <h1 className="text-4xl font-bold tracking-tight">
              Causal Insights Platform
            </h1>

            <p className="text-lg text-gray-600">
              Access experiment results, generate causal insights reports, and
              chat with an AI assistant to understand your data.
            </p>

            <form onSubmit={handleSubmit} className="pt-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <Input
                  id="run-id"
                  placeholder="Enter experiment run ID"
                  value={runId}
                  onChange={(e) => setRunId(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit" className="gap-2">
                  <span>Analyze</span>
                  <ArrowRight size={16} />
                </Button>
              </div>
            </form>
          </div>

          {/* Right Section - Features Card */}
          <Card className="bg-white shadow-md border-0">
            <CardHeader>
              <CardTitle>Features</CardTitle>
              <CardDescription>
                What can you do with the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">💬 Chat with your data</h3>
                <p className="text-sm text-gray-600">
                  Ask questions about your experiment and get clear, data-driven
                  answers
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">📊 Causal insights</h3>
                <p className="text-sm text-gray-600">
                  Understand cause-and-effect relationships in your experimental
                  data
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">🧠 Mindset analysis</h3>
                <p className="text-sm text-gray-600">
                  Explore how different user mindsets affect experimental
                  outcomes
                </p>
              </div>
            </CardContent>
            <CardFooter className="text-sm text-gray-500">
              Powered by Google Gemini AI
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
