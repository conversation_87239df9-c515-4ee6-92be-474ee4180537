import { useEffect, useState } from "react";
import { useExperiment } from "../hooks/use-experiment";
import ChatInterface from "../components/experiment/ChatInterface";
import LoadingState from "../components/ui/loading-state";
import ErrorState from "../components/ui/error-state";
import { useParams } from "wouter";

export default function Experiment() {
  // No longer need to track summary visibility, always auto-display chat
  const [runId, setRunId] = useState<string | null>(null);

  // Get run ID directly from path parameter (for /experiment/:runId)
  const params = useParams<{ runId: string }>();

  const { experimentData, reportText, reportPdfUrl, isLoading, isError, error, retry } =
    useExperiment(runId);

  // Get run_id from URL path parameter on initial load
  useEffect(() => {
    if (params && params.runId) {
      console.log(`Setting run ID from path parameter: ${params.runId}`);
      setRunId(params.runId);
    } else {
      // Redirect to home if no run_id is provided in the path
      window.location.href = "/";
    }
  }, [params]);

  if (isLoading) {
    return (
      <LoadingState
        message="Loading Experiment Data"
        description="Fetching experiment details and generating causal insights report..."
      />
    );
  }

  if (isError) {
    return (
      <ErrorState
        message="Error Loading Data"
        description={
          error instanceof Error
            ? error.message
            : "An unexpected error occurred"
        }
        retryAction={retry}
      />
    );
  }

  return (
    <div className="flex flex-col min-h-screen w-full bg-white">
      {/* Full-height interface with minimal UI */}
      <ChatInterface
        experimentData={experimentData}
        reportText={reportText}
        reportPdfUrl={reportPdfUrl}
        runId={runId || ""}
        summaryVisible={false} // Always hide summary by default, like ChatGPT
        toggleSummary={() => {}} // Empty function as we're not showing summary button
        toggleSidebar={() => {}} // Empty function as sidebar is removed
      />
    </div>
  );
}
