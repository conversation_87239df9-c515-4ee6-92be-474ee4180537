import { useState, useEffect, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { ArtifactResponse } from "@/lib/api-types";
import { fetchExperimentArtifacts, generateReport } from "../lib/api";

export function useExperiment(runId: string | null) {
  const [experimentData, setExperimentData] = useState<ArtifactResponse | null>(
    null
  );
  const [reportText, setReportText] = useState<string>("");
  const [reportPdfUrl, setReportPdfUrl] = useState<string | null>(null);

  // Fetch experiment artifacts with retry and error handling
  const artifactsQuery = useQuery({
    queryKey: ["/api/get-experiment-artifact", runId],
    queryFn: async () => {
      if (!runId) return null;
      try {
        const result = await fetchExperimentArtifacts(runId);
        return result;
      } catch (error) {
        console.error("Error fetching artifacts:", error);
        throw error;
      }
    },
    enabled: !!runId,
    staleTime: Infinity,
    retry: 2, // Retry failed requests up to 2 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff
  });

  // Generate report based on artifacts with retry and error handling
  const reportQuery = useQuery({
    queryKey: ["/api/generate-report", runId],
    queryFn: async () => {
      if (!runId) return null;
      try {
        const result = await generateReport(runId);
        return result;
      } catch (error) {
        console.error("Error generating report:", error);
        throw error;
      }
    },
    enabled: !!runId,
    staleTime: Infinity,
    retry: 2, // Retry failed requests up to 2 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff
  });

  // Update state when artifacts query completes
  useEffect(() => {
    if (artifactsQuery.data) {
      if (artifactsQuery.data.success) {
        console.log("Successfully fetched experiment artifacts");
        setExperimentData(artifactsQuery.data);
      } else {
        // Log the error but don't show it as a console.error to avoid red error messages
        console.log("Artifact query returned success: false -", artifactsQuery.data.error || "No error details available");

        // Even if the query failed, set a minimal experimentData object to prevent null references
        setExperimentData({
          success: false,
          experiment_definition_file: "",
          analytics_output_file: "",
          experiment_definition_data: {
            title: `Experiment ${runId || "Unknown"}`,
            context: "No experiment context available",
            target_behavior: "Unknown target behavior",
            country: "Unknown",
            year: new Date().getFullYear(),
          },
          error: artifactsQuery.data.error || "Failed to fetch experiment data"
        });
      }
    } else if (artifactsQuery.isError) {
      // Handle error state
      console.log("Artifact query error:", artifactsQuery.error);

      // Set minimal experimentData object
      setExperimentData({
        success: false,
        experiment_definition_file: "",
        analytics_output_file: "",
        experiment_definition_data: {
          title: `Experiment ${runId || "Unknown"}`,
          context: "No experiment context available",
          target_behavior: "Unknown target behavior",
          country: "Unknown",
          year: new Date().getFullYear(),
        },
        error: artifactsQuery.error instanceof Error ? artifactsQuery.error.message : "Failed to fetch experiment data"
      });
    }
  }, [artifactsQuery.data, artifactsQuery.isError, artifactsQuery.error, runId]);

  // Update state when report query completes
  useEffect(() => {
    if (reportQuery.data) {
      if (reportQuery.data.success) {
        if (reportQuery.data.pdfUrl) {
          // Handle PDF report with URL
          console.log("Received PDF report URL:", reportQuery.data.pdfUrl);
          setReportPdfUrl(reportQuery.data.pdfUrl);
          setReportText(
            "A report has been generated for this experiment."
          );
        } else if (reportQuery.data.report_text) {
          // Check if the report_text is actually PDF content
          if (reportQuery.data.report_text.startsWith('%PDF')) {
            console.log("Detected PDF content in report_text");
            // Create a blob URL for the PDF content
            const encoder = new TextEncoder();
            const pdfBytes = encoder.encode(reportQuery.data.report_text);
            const blob = new Blob([pdfBytes], { type: 'application/pdf' });
            const pdfUrl = URL.createObjectURL(blob);

            setReportPdfUrl(pdfUrl);
            setReportText(
              "A report has been generated for this experiment."
            );
          } else {
            // Handle regular text report
            console.log(
              "Report query data (text):",
              reportQuery.data.report_text.substring(0, 100) + "..."
            );
            setReportText(reportQuery.data.report_text);
          }
        } else {
          // No report content
          console.log("Report query successful but no content");
          setReportText(
            "Unable to generate a complete report for this experiment. " +
            "You can still ask questions about the experiment data."
          );
        }
      } else {
        console.log("Report query data (not successful):", reportQuery.data);

        // Check if the raw data is a PDF
        const responseData = reportQuery.data as any;
        if (typeof responseData === 'string' && responseData.startsWith('%PDF')) {
          console.log("Detected PDF content in unsuccessful response");
          // Create a blob URL for the PDF content
          const encoder = new TextEncoder();
          const pdfBytes = encoder.encode(responseData);
          const blob = new Blob([pdfBytes], { type: 'application/pdf' });
          const pdfUrl = URL.createObjectURL(blob);

          setReportPdfUrl(pdfUrl);
          setReportText(
            "A report has been generated for this experiment."
          );
        } else {
          // Set a fallback report text if the query was not successful
          setReportText(
            "Unable to generate a complete report for this experiment. " +
            "You can still ask questions about the experiment data."
          );
        }
      }
    }
  }, [reportQuery.data]);

  // Combined loading state
  const isLoading = artifactsQuery.isLoading || reportQuery.isLoading;

  // Combined error state
  const isError =
    artifactsQuery.isError ||
    reportQuery.isError ||
    (artifactsQuery.data && !artifactsQuery.data.success) ||
    (reportQuery.data && !reportQuery.data.success);

  // Error message formatting
  const error = getErrorMessage(artifactsQuery, reportQuery);

  // Retry function
  const retry = useCallback(() => {
    if (
      artifactsQuery.isError ||
      (artifactsQuery.data && !artifactsQuery.data.success)
    ) {
      console.log("Retrying artifacts query...");
      artifactsQuery.refetch();
    }

    if (
      reportQuery.isError ||
      (reportQuery.data && !reportQuery.data.success)
    ) {
      console.log("Retrying report query...");
      reportQuery.refetch();
    }
  }, [artifactsQuery, reportQuery]);

  return {
    experimentData,
    reportText,
    reportPdfUrl,
    isLoading,
    isError,
    error,
    retry,
  };
}

// Helper to get appropriate error message
function getErrorMessage(artifactsQuery: any, reportQuery: any): Error {
  if (artifactsQuery.error) {
    return artifactsQuery.error;
  }

  if (reportQuery.error) {
    return reportQuery.error;
  }

  if (artifactsQuery.data && !artifactsQuery.data.success) {
    return new Error(
      artifactsQuery.data.error || "Failed to fetch experiment data"
    );
  }

  if (reportQuery.data && !reportQuery.data.success) {
    return new Error(reportQuery.data.error || "Failed to generate report");
  }

  return new Error("Unknown error occurred");
}
