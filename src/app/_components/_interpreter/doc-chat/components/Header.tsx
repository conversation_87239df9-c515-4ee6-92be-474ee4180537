import { <PERSON> } from "wouter";
import { Flame } from "lucide-react";

export default function Header() {
  return (
    <header className="sticky top-0 z-40 w-full border-b border-b-slate-200 bg-white">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <Link href="/" className="flex items-center space-x-2">
            <Flame className="text-indigo-600" />
            <span className="font-bold">Causal Insights Platform</span>
          </Link>
        </div>
        <div className="flex flex-1 items-center space-x-2 justify-end">
          {/* Navigation links removed as requested */}
        </div>
      </div>
    </header>
  );
}
