import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface ErrorStateProps {
  message: string;
  description?: string;
  retryAction?: () => void;
}

export default function ErrorState({
  message,
  description,
  retryAction,
}: ErrorStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <div className="flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
        <AlertTriangle className="h-8 w-8 text-red-600" />
      </div>
      <h2 className="text-xl font-semibold text-slate-800">{message}</h2>
      {description && (
        <p className="text-slate-600 text-center max-w-md">{description}</p>
      )}
      {retryAction && <Button onClick={retryAction}>Retry</Button>}
    </div>
  );
}
