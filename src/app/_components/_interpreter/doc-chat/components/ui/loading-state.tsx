import { Button } from "@/components/ui/button";

interface LoadingStateProps {
  message: string;
  description?: string;
  action?: () => void;
  actionText?: string;
}

export default function LoadingState({
  message,
  description,
  action,
  actionText = "Cancel",
}: LoadingStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <div className="relative h-16 w-16">
        <div className="absolute animate-spin rounded-full h-16 w-16 border-t-4 border-indigo-600"></div>
        <div className="absolute rounded-full h-16 w-16 border-4 border-slate-200"></div>
      </div>
      <h2 className="text-xl font-semibold text-slate-800">{message}</h2>
      {description && (
        <p className="text-slate-600 text-center max-w-md">{description}</p>
      )}
      {action && (
        <Button variant="outline" onClick={action}>
          {actionText}
        </Button>
      )}
    </div>
  );
}
