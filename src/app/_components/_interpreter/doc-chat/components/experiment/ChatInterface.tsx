import React, { useState, useRef, useEffect, useCallback } from "react";
import { Button } from "../../components/ui/button";
import { Textarea } from "../../components/ui/textarea";
import { Send, Info, Search, Download } from "lucide-react";
import "../../chat-fixes.css";
import { useChat } from "../../hooks/use-chat-unified";
import { ArtifactResponse } from "@/lib/api-types";
import ChatMessage from "./ChatMessage";
import DataAccessError from "./DataAccessError";
import { extractExperimentTitle } from "@/utils/message-utils";
import { downloadReport, openPdfInNewTab, isPdfContent } from "@/utils/pdf-utils";
import { createScrollToBottom, scrollToBottomWithDelay } from "@/utils/scroll-utils";

interface ChatInterfaceProps {
  experimentData: ArtifactResponse | null;
  reportText: string;
  reportPdfUrl?: string | null;
  runId: string;
  // These props are kept for interface compatibility but not used in this component
  summaryVisible?: boolean;
  toggleSummary?: () => void;
  toggleSidebar?: () => void;
}

export default function ChatInterface({ experimentData, reportText, runId, summaryVisible, toggleSummary }: ChatInterfaceProps) {
  const [userInput, setUserInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Use a safe version of reportText that's never empty
  const safeReportText = reportText ||
    `# Causal Insights Analysis\n\nThis is a placeholder report for experiment ${runId}. The actual report data could not be loaded.`;

  // Use our enhanced chat hook with logging enabled in development
  const { messages, isLoading, sendMessage } = useChat(
    runId, 
    safeReportText, 
    { enableLogging: process.env.NODE_ENV === 'development' }
  );

  // Improved scroll handling
  const scrollToBottom = useCallback(() => {
    if (!messagesEndRef.current || !chatContainerRef.current) return;

    try {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      });
    } catch (error) {
      // Fallback for browsers that don't support smooth scrolling
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, []);

  // Enhanced scroll effect with proper cleanup
  useEffect(() => {
    const scrollTimers = [
      setTimeout(scrollToBottom, 0),   // Immediate scroll
      setTimeout(scrollToBottom, 100), // After initial render
      setTimeout(scrollToBottom, 300)  // After images might have started loading
    ];

    // Scroll on new messages
    scrollToBottom();

    return () => {
      // Cleanup timers
      scrollTimers.forEach(timer => clearTimeout(timer));
    };
  }, [messages, scrollToBottom]);

  // State to track data access errors
  const [dataAccessError, setDataAccessError] = useState<string | null>(null);

  // Detect data access errors in report text or experiment data
  useEffect(() => {
    if (
      reportText &&
      reportText.includes("system error") &&
      reportText.includes("file is inaccessible")
    ) {
      setDataAccessError(
        "The experiment definition file is inaccessible. You may not have permission to access this data."
      );
    } else if (
      experimentData?.success === true &&
      (!experimentData.experiment_definition_data ||
        Object.keys(experimentData.experiment_definition_data).length === 0)
    ) {
      setDataAccessError(
        "Unable to retrieve complete experiment data. This may be due to permission issues."
      );
    } else {
      setDataAccessError(null);
    }
  }, [reportText, experimentData]);

  // Auto resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "inherit";
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 150)}px`;
    }
  }, [userInput]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (userInput.trim() && !isLoading) {
      sendMessage(userInput);
      setUserInput("");

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = "inherit";
      }
    }
  };

  // Get experiment title using our utility function
  const getExperimentTitle = useCallback(() => {
    // Try to get a more descriptive title from experiment data first
    let title = experimentData?.experiment_definition_data?.title || "";

    // If no title found or it contains underscores, extract from report text
    if (!title || title.includes("_")) {
      title = extractExperimentTitle(reportText);
    }

    return title;
  }, [experimentData, reportText]);

  // Function to download the causal report as a text file or PDF
  const handleDownloadReport = useCallback(async () => {
    if (!reportText) return;

    // Check if the report is a notification message
    const isNotificationMessage = reportText.includes("A report has been generated");

    if (isNotificationMessage) {
      // If it's a notification message, we need to fetch the actual report content
      try {
        // Fetch the report directly from the API
        const response = await fetch(`/api/generate-report`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            run_id: runId,
            format: "text",
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch report: ${response.status}`);
        }

        // Check if the response is a PDF by looking at the content type
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/pdf')) {
          // Handle PDF response directly
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `Causal_Report_${runId.substring(0, 8)}.pdf`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          return;
        }

        // For non-PDF responses, try to download as text
        try {
          // Try to get the response as text first
          const responseText = await response.text();

          // Check if the text is actually PDF content
          if (isPdfContent(responseText)) {
            // Use our utility function to download the PDF
            downloadReport(responseText, runId, true);
            return;
          }

          // Try to parse as JSON
          try {
            const data = JSON.parse(responseText);

            if (data.report_text) {
              // Check if the report_text is actually PDF content
              if (isPdfContent(data.report_text)) {
                // Use our utility function to download the PDF
                downloadReport(data.report_text, runId, true);
                return;
              }

              // Use the report text from the API response
              downloadReport(data.report_text, runId, false);
              return;
            } else if (data.pdfUrl) {
              // If the API returns a PDF URL, download it
              openPdfInNewTab(data.pdfUrl);
              return;
            } else {
              // If no report text or PDF URL, download the raw response
              downloadReport(responseText, runId, false);
              return;
            }
          } catch (jsonError) {
            // If JSON parsing fails, download the raw response text
            downloadReport(responseText, runId, false);
            return;
          }
        } catch (textError) {
          alert("Failed to download report. Please try again later.");
          return;
        }
      } catch (error) {
        alert("Failed to download report. Please try again later.");
        return;
      }
    }

    // Check if the reportText is actually PDF content
    if (isPdfContent(reportText)) {
      // Use our utility function to download the PDF
      downloadReport(reportText, runId, true);
      return;
    }

    // For regular text reports, proceed with text download
    downloadReport(reportText, runId, false);
  }, [reportText, runId]);

  return (
    <div className="flex flex-col h-full w-full chat-container">
      {/* Header - Fixed at top */}
      <header className="bg-gradient-to-r from-indigo-50 via-blue-50 to-indigo-50 border-b border-gray-200 py-4 px-4 sm:px-6 sticky top-0 z-10 w-full shadow-lg backdrop-blur-md bg-white/70">
        <div className="flex items-center justify-between w-full max-w-3xl mx-auto">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-br from-indigo-500 to-blue-600 w-11 h-11 rounded-xl flex items-center justify-center shadow-md animate-pulseGlow">
              <div className="text-white font-bold text-base">CI</div>
            </div>
            <div className="flex flex-col">
              <h1
                className="text-lg font-semibold text-gradient inline-block max-w-[240px] sm:max-w-[400px] overflow-hidden text-ellipsis whitespace-nowrap"
                title={getExperimentTitle()}
              >
                {getExperimentTitle()}
              </h1>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Download Report Button - Always show if we have any report */}
            {reportText && (
              <div className="group relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDownloadReport}
                  title="Download Report"
                  className="rounded-full h-9 w-9 flex-shrink-0 hover:bg-indigo-100 transition-colors button-glow"
                >
                  <Download className="h-4.5 w-4.5 text-indigo-700" />
                </Button>
                
                <div className="absolute right-0 top-full mt-2 w-60 max-w-[90vw] rounded-lg glass-card p-3 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-20 animate-fadeIn">
                  <p className="text-gray-600 leading-relaxed">
                    Download the full causal insights report as a text file
                  </p>
                </div>
              </div>
            )}

            {/* Info Button */}
            <div className="group relative">
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full h-9 w-9 flex-shrink-0 hover:bg-indigo-100 transition-colors button-glow"
              >
                <Info className="h-4.5 w-4.5 text-indigo-700" />
              </Button>

              <div className="absolute right-0 top-full mt-2 w-80 max-w-[90vw] rounded-lg glass-card p-4 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-20 animate-fadeIn">
                <div className="flex items-center mb-2">
                  <div className="w-1 h-5 bg-gradient-to-b from-indigo-600 to-blue-600 rounded-full mr-2"></div>
                  <p className="font-medium text-gradient">
                    Causal Insights Platform
                  </p>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  This AI-powered assistant analyzes experiment data to provide
                  causal insights. Ask questions about the experiment results,
                  methodology, or specific findings to understand what factors
                  have the strongest impact.
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Chat Messages Container - Taking remaining height with improved scrolling */}
      <div
        ref={chatContainerRef}
        className="flex-grow w-full px-4 sm:px-6 py-6 overflow-y-auto custom-scrollbar messages-container"
      >
        <div className="w-full max-w-3xl mx-auto pb-20">
          {/* Data Access Error Display */}
          {dataAccessError && messages.length > 0 && (
            <DataAccessError
              message={dataAccessError}
              onRetry={() => window.location.reload()}
            />
          )}

          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-96 mt-8">
              <div className="relative mb-8">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 flex items-center justify-center shadow-lg animate-pulseGlow">
                  <div className="absolute inset-0 rounded-full border-t-4 border-l-4 border-white/30 animate-spin"></div>
                  <div className="h-10 w-10 rounded-full glass-effect flex items-center justify-center">
                    <div className="text-gradient font-semibold">CI</div>
                  </div>
                </div>
              </div>
              <h3 className="text-gradient font-semibold text-lg mb-2">
                Processing Experiment Data
              </h3>
              <p className="text-gray-600 text-center max-w-md mb-4">
                Analyzing causal relationships and preparing insights based on
                your experiment data.
              </p>
              <div className="flex space-x-2 items-center">
                <div className="h-2 w-2 bg-indigo-400 rounded-full animate-ping"></div>
                <div
                  className="h-2 w-2 bg-indigo-500 rounded-full animate-ping"
                  style={{ animationDelay: "0.2s" }}
                ></div>
                <div
                  className="h-2 w-2 bg-indigo-600 rounded-full animate-ping"
                  style={{ animationDelay: "0.4s" }}
                ></div>
              </div>
            </div>
          ) : (
            messages.map((message, index: number) => (
              <ChatMessage key={index} message={message} index={index} />
            ))
          )}

          {/* Auto-scroll anchor */}
          <div ref={messagesEndRef} />

          {/* Loading indicator */}
          {isLoading && (
            <div className="py-5 flex justify-center animate-fadeIn">
              <div className="flex items-center space-x-4 glass-card px-5 py-3">
                <div className="relative">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 flex items-center justify-center shadow-md animate-pulseGlow">
                    <div className="absolute inset-0 rounded-full border-t-2 border-indigo-200 animate-spin"></div>
                    <div className="h-4 w-4 rounded-full bg-white"></div>
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="text-sm font-medium text-gradient">
                    Analyzing causal data...
                  </div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    Identifying key relationships and insights
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chat Input - Fixed at bottom with improved positioning */}
      <div className="bg-gradient-to-r from-indigo-50 via-blue-50 to-indigo-50 border-t border-gray-200 py-4 px-4 sm:px-6 sticky bottom-0 left-0 right-0 z-10 w-full shadow-[0_-2px_10px_rgba(0,0,0,0.05)] backdrop-blur-md">
        <div className="w-full max-w-3xl mx-auto">
          <form
            onSubmit={handleSubmit}
            className="flex items-end gap-2 glass-card p-3 transition-all focus-within:shadow-md focus-within:border-indigo-300 hover:shadow-lg"
          >
            <div className="flex-1 min-w-0 relative">
              <Textarea
                ref={textareaRef}
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="Ask about causal effects, experiment details, or recommendations..."
                className="resize-none min-h-[24px] max-h-[150px] overflow-y-auto border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-2 w-full text-gray-800 placeholder:text-gray-400 custom-scrollbar"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
                disabled={isLoading}
              />
              {!userInput && (
                <div className="absolute left-3 bottom-2 text-xs text-gray-400/80 pointer-events-none">
                  <div className="flex items-center">
                    <Search className="w-3 h-3 mr-1.5 text-indigo-400" />
                    <span>Type a question to analyze causal relationships</span>
                  </div>
                </div>
              )}
            </div>
            <Button
              type="submit"
              disabled={!userInput.trim() || isLoading}
              size="icon"
              className={`rounded-full p-2 h-11 w-11 flex-shrink-0 bg-gradient-to-br from-indigo-500 to-blue-600 shadow-md transition-all button-glow ${!userInput.trim() || isLoading ? "opacity-50" : "hover:opacity-90"}`}
            >
              <Send className="h-5 w-5 text-white" />
            </Button>
          </form>
          <div className="flex justify-center mt-2">
            <div className="text-xs text-indigo-700/70 font-medium glass-effect px-4 py-1.5 rounded-full shadow-sm">
              Press{" "}
              <kbd className="px-1.5 py-0.5 text-xs bg-white/80 border border-indigo-200 rounded-md mx-1 font-mono shadow-sm">
                Enter
              </kbd>{" "}
              to send,{" "}
              <kbd className="px-1.5 py-0.5 text-xs bg-white/80 border border-indigo-200 rounded-md mx-1 font-mono shadow-sm">
                Shift+Enter
              </kbd>{" "}
              for a new line
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
