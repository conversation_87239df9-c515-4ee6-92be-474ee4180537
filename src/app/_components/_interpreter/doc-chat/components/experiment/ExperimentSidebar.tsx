import { Button } from "@/components/ui/button";
import { ArtifactResponse } from "@/lib/api-types";

interface ExperimentSidebarProps {
  experimentData: ArtifactResponse | null;
  runId: string;
  toggleSidebar: () => void;
}

export default function ExperimentSidebar({
  experimentData,
  runId,
  toggleSidebar,
}: ExperimentSidebarProps) {
  const expData = experimentData?.experiment_definition_data || {};

  return (
    <div className="sticky top-20">
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Experiment Details</h2>
        </div>

        <div className="p-4 space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Run ID</h3>
            <p className="text-sm font-mono">{runId}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Title</h3>
            <p className="text-sm">{expData.title || "Not available"}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">
              Target Behavior
            </h3>
            <p className="text-sm">
              {expData.target_behavior || "Not available"}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Country</h3>
            <p className="text-sm">{expData.country || "Not available"}</p>
          </div>

          {expData.year && (
            <div>
              <h3 className="text-sm font-medium text-gray-500">Year</h3>
              <p className="text-sm">{expData.year}</p>
            </div>
          )}

          {expData.context && (
            <div>
              <h3 className="text-sm font-medium text-gray-500">Context</h3>
              <p className="text-sm">{expData.context}</p>
            </div>
          )}
        </div>

        <div className="p-4 border-t border-gray-200">
          <Button onClick={toggleSidebar} className="w-full" variant="outline">
            Hide Sidebar
          </Button>
        </div>
      </div>
    </div>
  );
}
