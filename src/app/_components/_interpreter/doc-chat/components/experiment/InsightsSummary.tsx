import { useState } from "react";
import { cn } from "@/lib/utils";
import {
  <PERSON>bulb,
  TrendingUp,
  TrendingDown,
  ChevronDown,
  ChevronRight,
  Users,
  <PERSON><PERSON>hart,
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface InsightsSummaryProps {
  experimentData: any;
  reportText: string;
  visible: boolean;
}

export default function InsightsSummary({
  experimentData,
  reportText,
  visible,
}: InsightsSummaryProps) {
  const [expanded, setExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "surprising" | "positive" | "negative" | "segments"
  >("surprising");

  if (!visible) return null;

  // Extract insights from structured experiment data and report text
  const extractInsights = () => {
    const insights = {
      surprising: [] as { title: string; description: string }[],
      positive: [] as { factor: string; impact: string }[],
      negative: [] as { factor: string; impact: string }[],
      segments: [] as { segment: string; insight: string }[],
    };

    try {
      // First try to extract insights from structured data if available
      if (experimentData && experimentData.analytics_output_data) {
        const analyticsData = experimentData.analytics_output_data;

        // Extract from AMCE data if available
        if (
          analyticsData.Ind_Est_2 &&
          analyticsData.Ind_Est_2.error_handling_calc?.Utility_Estimation_calc
            ?.AMCE_Data
        ) {
          const amceData =
            analyticsData.Ind_Est_2.error_handling_calc.Utility_Estimation_calc
              .AMCE_Data;

          // Sort by AMCE value (absolute)
          const sortedData = [...amceData].sort(
            (a, b) => Math.abs(b.AMCE || 0) - Math.abs(a.AMCE || 0)
          );

          // Extract positive impacts
          const positiveImpacts = sortedData
            .filter((item) => (item.AMCE || 0) > 0)
            .slice(0, 5);

          positiveImpacts.forEach((item) => {
            const factor = item.attribute_text || "Unknown factor";
            const impact = `+${(item.AMCE * 100).toFixed(1)}% effect`;
            insights.positive.push({ factor, impact });
          });

          // Extract negative impacts
          const negativeImpacts = sortedData
            .filter((item) => (item.AMCE || 0) < 0)
            .slice(0, 5);

          negativeImpacts.forEach((item) => {
            const factor = item.attribute_text || "Unknown factor";
            const impact = `${(item.AMCE * 100).toFixed(1)}% effect`;
            insights.negative.push({ factor, impact });
          });

          // Extract surprising findings (those with high absolute effect but low p-value)
          const surprisingFindings = sortedData
            .filter(
              (item) =>
                Math.abs(item.AMCE || 0) > 0.05 && (item.p_value || 1) < 0.05
            )
            .slice(0, 5);

          surprisingFindings.forEach((item) => {
            insights.surprising.push({
              title: `${item.attribute_text || "Factor"} has a strong ${(item.AMCE || 0) > 0 ? "positive" : "negative"} effect`,
              description: `${item.level_text || "This level"} shows a ${Math.abs((item.AMCE || 0) * 100).toFixed(1)}% impact on preferences`,
            });
          });

          // Extract segment insights if available
          if (analyticsData.Ind_Est_2.mindset_partworth) {
            const mindsetData = analyticsData.Ind_Est_2.mindset_partworth;

            // Process mindset data
            for (const item of mindsetData) {
              if (item.mindset && item.attribute_text) {
                insights.segments.push({
                  segment: item.mindset,
                  insight: `${item.mindset} shows ${Math.abs((item.AMCE || 0) * 100).toFixed(1)}% ${(item.AMCE || 0) > 0 ? "higher" : "lower"} preference for ${item.attribute_text}`,
                });
              }
            }

            // Limit to top 5
            insights.segments = insights.segments.slice(0, 5);
          }
        }
      }

      // If we couldn't extract from structured data, fall back to text analysis
      if (insights.positive.length === 0 && insights.negative.length === 0) {
        // Extract insights from report text as a fallback
        const sentences = reportText.split(/[.!?]\s+/);

        // Extract positive impacts with improved patterns
        const positivePatterns = [
          /([^,.]+) (?:had|showed|demonstrated) (?:a |an |)(?:significant |strong |)positive impact/i,
          /([^,.]+) increased (?:the |)(?:likelihood|probability|chance|preference)/i,
          /([^,.]+) improved (?:the |)(?:outcome|result|performance|rating)/i,
        ];

        for (const sentence of sentences) {
          for (const pattern of positivePatterns) {
            const match = sentence.match(pattern);
            if (match) {
              const factor = match[1].trim();
              // Extract impact value if present
              const percentMatch = sentence.match(/(\d+(?:\.\d+)?)%/);
              const impact = percentMatch
                ? `+${percentMatch[1]}% effect`
                : "positive impact";

              if (factor && !factor.includes("negative") && factor.length > 3) {
                insights.positive.push({ factor, impact });
                break; // Break after first match for this sentence
              }
            }
          }
        }

        // Extract negative impacts with improved patterns
        const negativePatterns = [
          /([^,.]+) (?:had|showed|demonstrated) (?:a |an |)(?:significant |strong |)negative impact/i,
          /([^,.]+) decreased (?:the |)(?:likelihood|probability|chance|preference)/i,
          /([^,.]+) reduced (?:the |)(?:outcome|result|performance|rating)/i,
        ];

        for (const sentence of sentences) {
          for (const pattern of negativePatterns) {
            const match = sentence.match(pattern);
            if (match) {
              const factor = match[1].trim();
              // Extract impact value if present
              const percentMatch = sentence.match(/(\d+(?:\.\d+)?)%/);
              const impact = percentMatch
                ? `-${percentMatch[1]}% effect`
                : "negative impact";

              if (factor && !factor.includes("positive") && factor.length > 3) {
                insights.negative.push({ factor, impact });
                break; // Break after first match for this sentence
              }
            }
          }
        }

        // Extract surprising findings
        const surprisingPatterns = [
          /(?:surprisingly|unexpectedly|interestingly|notably),?\s+([^,.]+)/i,
          /([^,.]+) was (?:surprising|unexpected|interesting|notable)/i,
          /(?:surprising|unexpected|interesting|notable) finding (?:was |is |)that ([^,.]+)/i,
        ];

        for (let i = 0; i < sentences.length; i++) {
          const sentence = sentences[i];

          for (const pattern of surprisingPatterns) {
            const match = sentence.match(pattern);
            if (match && i < sentences.length - 1) {
              const title = match[1].trim();
              const description = sentences[i + 1].trim();

              if (title.length > 3) {
                insights.surprising.push({ title, description });
                break; // Break after first match for this sentence
              }
            }
          }
        }

        // Extract segment-specific insights
        const segmentPatterns = [
          /(?:segment|demographic|audience|group) (?:called |labeled |known as |)"?([^",.]+)"?/i,
          /(?:for|among) (?:the |)"?([^",.]+)"? (?:segment|demographic|audience|group)/i,
        ];

        for (const sentence of sentences) {
          for (const pattern of segmentPatterns) {
            const match = sentence.match(pattern);
            if (match) {
              const segment = match[1].trim();

              if (
                segment &&
                segment.length > 3 &&
                !segment.match(/^(a|an|the)$/i)
              ) {
                insights.segments.push({
                  segment,
                  insight: sentence.trim(),
                });
                break; // Break after first match for this sentence
              }
            }
          }
        }
      }

      // Limit to top 5 of each and ensure no duplicates
      insights.surprising = Array.from(
        new Map(insights.surprising.map((item) => [item.title, item])).values()
      ).slice(0, 5);

      insights.positive = Array.from(
        new Map(insights.positive.map((item) => [item.factor, item])).values()
      ).slice(0, 5);

      insights.negative = Array.from(
        new Map(insights.negative.map((item) => [item.factor, item])).values()
      ).slice(0, 5);

      insights.segments = Array.from(
        new Map(insights.segments.map((item) => [item.segment, item])).values()
      ).slice(0, 5);

      // If any sections are empty, generate intelligent defaults based on experiment data
      if (insights.surprising.length === 0) {
        // Try to generate a meaningful default based on experiment data
        const title =
          experimentData?.experiment_definition_data?.title ||
          "this experiment";

        insights.surprising.push({
          title: `Significant variation in user preferences for ${title}`,
          description:
            "Different user segments showed notably different preferences for the same features",
        });
      }

      return insights;
    } catch (error) {
      console.error("Error processing insights:", error);
      return {
        surprising: [
          {
            title: "Insights available in full report",
            description:
              "View the complete report for detailed causal insights",
          },
        ],
        positive: [],
        negative: [],
        segments: [],
      };
    }
  };

  const insights = extractInsights();

  return (
    <div
      className={cn(
        "w-full max-w-3xl mx-auto mb-6 overflow-hidden transition-all duration-300",
        "glass-card p-4 rounded-xl border border-indigo-100/50 hover:shadow-lg",
        !expanded ? "max-h-16" : "max-h-[550px]"
      )}
    >
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center gap-2">
          <div
            className={cn(
              "rounded-full p-1.5 transition-colors",
              expanded ? "bg-indigo-100 text-indigo-600" : "text-indigo-500"
            )}
          >
            <Lightbulb className="h-5 w-5" />
          </div>
          <h3 className="font-medium text-gradient">Key Causal Insights</h3>
          <div className="text-xs bg-indigo-100 text-indigo-700 px-2 py-0.5 rounded-full font-medium ml-2">
            Structured Analysis
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "p-1 h-6 w-6 transition-transform duration-300",
            expanded ? "rotate-180" : "rotate-0"
          )}
        >
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>

      {expanded && (
        <div className="mt-4 animate-slideDown">
          {/* Tabs */}
          <div className="flex justify-center mb-4 flex-wrap">
            <div className="flex bg-slate-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveTab("surprising")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "surprising"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <Lightbulb className="h-3 w-3" />
                <span>Surprising</span>
              </button>
              <button
                onClick={() => setActiveTab("positive")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "positive"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <TrendingUp className="h-3 w-3" />
                <span>Positive</span>
              </button>
              <button
                onClick={() => setActiveTab("negative")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "negative"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <TrendingDown className="h-3 w-3" />
                <span>Negative</span>
              </button>
              <button
                onClick={() => setActiveTab("segments")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "segments"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <Users className="h-3 w-3" />
                <span>Segments</span>
              </button>
            </div>
          </div>

          {/* Content based on active tab */}
          <div className="p-1">
            {activeTab === "surprising" && (
              <div className="space-y-3">
                {insights.surprising.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-indigo-50/30 border border-indigo-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-indigo-100 p-1.5 mt-0.5">
                        <Lightbulb className="h-3.5 w-3.5 text-indigo-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-indigo-900">
                          {item.title}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "positive" && (
              <div className="space-y-3">
                {insights.positive.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-green-50/30 border border-green-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-green-100 p-1.5 mt-0.5">
                        <TrendingUp className="h-3.5 w-3.5 text-green-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-green-900">
                          {item.factor}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          Impact: {item.impact}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "negative" && (
              <div className="space-y-3">
                {insights.negative.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-red-50/30 border border-red-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-red-100 p-1.5 mt-0.5">
                        <TrendingDown className="h-3.5 w-3.5 text-red-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-red-900">
                          {item.factor}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          Impact: {item.impact}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "segments" && (
              <div className="space-y-3">
                {insights.segments.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-blue-50/30 border border-blue-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-blue-100 p-1.5 mt-0.5">
                        <Users className="h-3.5 w-3.5 text-blue-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-blue-900">
                          {item.segment}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          {item.insight}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
            <p className="text-xs text-gray-500">
              Insights automatically extracted from causal analysis
            </p>
            <div className="text-xs bg-indigo-100 text-indigo-600 px-2 py-1 rounded-full font-medium">
              AI-powered
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
