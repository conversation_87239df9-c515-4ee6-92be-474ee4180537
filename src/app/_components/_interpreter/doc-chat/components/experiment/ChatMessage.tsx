import { ChatMessage as ChatMessageType } from "@/lib/api-types";
import { cn } from "@/lib/utils";
import ReactMarkdown from "react-markdown";
import { User, Bot, Download } from "lucide-react";
import { useEffect, useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { processAssistantMessageContent, messageContainsReport } from "@/utils/message-utils";
import { downloadReport } from "@/utils/pdf-utils";

interface ChatMessageProps {
  message: ChatMessageType;
  index?: number;
}

export default function ChatMessage({ message, index = 0 }: ChatMessageProps) {
  // Don't render system messages
  if (message.role === "system") {
    return null;
  }
  
  const isUser = message.role === "user";
  const [showAnimation, setShowAnimation] = useState(true);
  const isFirstAssistantMessage = !isUser && index === 1; // First assistant message is usually index 1

  // Disable the fade-in animation after it plays once
  useEffect(() => {
    const timer = setTimeout(() => setShowAnimation(false), 500);
    return () => clearTimeout(timer);
  }, []);

  // Process content with enhanced formatting
  const processedContent = useMemo(() => {
    if (isUser) return message.content;
    
    // Apply the base processing from the utility
    let content = processAssistantMessageContent(message.content);
    
    // Additional formatting fixes for specific patterns
    
    // Fix price range formatting
    content = content.replace(
      /\$(\d+)-\$(\d+)/g, 
      "**$$$1-$$$2**"
    );
    
    // Fix mindset data formatting
    const mindsetPatterns = [
      {
        pattern: /Social Sippers(?:\s+with\s+a\s+Sweet\s+Tooth)?(?:\s+\(?([\d.]+)%\)?)?/g,
        replacement: "**Social Sippers with a Sweet Tooth** ($1%)"
      },
      {
        pattern: /Alertness Aficionados(?:\s+on\s+the\s+Go)?(?:\s+\(?([\d.]+)%\)?)?/g,
        replacement: "**Alertness Aficionados on the Go** ($1%)"
      },
      {
        pattern: /Flavor-First Value(?:\s+Sippers)?(?:\s+\(?([\d.]+)%\)?)?/g,
        replacement: "**Flavor-First Value Sippers** ($1%)"
      },
      {
        pattern: /Health-Conscious(?:\s+Home\s+Brewers)?(?:\s+\(?([\d.]+)%\)?)?/g,
        replacement: "**Health-Conscious Home Brewers** ($1%)"
      }
    ];
    
    // Apply each mindset pattern
    mindsetPatterns.forEach(({ pattern, replacement }) => {
      content = content.replace(pattern, replacement);
    });
    
    // Format the mindset section with proper list structure
    if (content.includes("identifies four consumer mindsets")) {
      content = content.replace(
        /(The report identifies four consumer mindsets:)/i,
        "$1\n\n"
      );
    }
    
    return content;
  }, [isUser, message.content]);

  // Function to handle report download
  const handleDownloadReport = () => {
    if (!message.content) return;
    
    // Use the utility function to download the report
    downloadReport(message.content, "experiment", false);
  };

  return (
    <div
      className={cn(
        "py-4 first:pt-0 w-full transition-colors",
        isUser ? "chat-message-user" : "chat-message-assistant",
        showAnimation && "animate-fadeIn"
      )}
    >
      <div className="flex gap-3 w-full max-w-3xl mx-auto py-2 px-3 rounded-lg transition-all">
        <div
          className={cn(
            "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mt-1 shadow-md",
            isUser
              ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white"
              : "bg-gradient-to-br from-indigo-500 to-indigo-700 text-white",
            isUser ? "" : "animate-pulseGlow"
          )}
        >
          {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
        </div>

        <div className="flex-1 min-w-0 pt-0.5">
          <div
            className={cn(
              "text-xs mb-1 font-medium flex items-center gap-1.5",
              isUser ? "text-blue-700" : "text-indigo-700"
            )}
          >
            {isUser ? (
              <>
                <span>You</span>
              </>
            ) : (
              <>
                <span>Causal Insights AI</span>
                <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              </>
            )}
          </div>

          {isUser ? (
            <p className="text-gray-800 leading-relaxed break-words chat-message-content">
              {processedContent}
            </p>
          ) : (
            <div className="relative">
              {/* Download button for any assistant message with report-like content */}
              {messageContainsReport(message) && (
                <div className="absolute right-0 top-0 flex items-center -mt-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDownloadReport}
                    title="Download Causal Report"
                    className="flex items-center space-x-1 text-xs text-indigo-600 hover:text-indigo-800 rounded-md py-1 px-2 bg-indigo-50 hover:bg-indigo-100 transition-colors"
                  >
                    <Download className="h-3 w-3" />
                    <span>Download Report</span>
                  </Button>
                </div>
              )}

              <div className="text-gray-800 prose prose-slate max-w-none prose-p:my-2 prose-headings:mb-3 prose-headings:mt-5 prose-headings:text-indigo-900 prose-h1:text-xl prose-h2:text-lg prose-h3:text-base prose-li:my-1.5 prose-li:pl-1 prose-strong:text-indigo-700 prose-strong:font-semibold break-words prose-pre:bg-gray-50 prose-pre:border prose-pre:border-gray-200 prose-pre:rounded-md prose-pre:p-2 prose-code:text-indigo-600 prose-code:bg-indigo-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded-sm prose-code:before:content-none prose-code:after:content-none custom-scrollbar chat-message-content">
                <ReactMarkdown
                  components={{
                    // Enhance list items with custom styling
                    li: ({ node, ...props }) => (
                      <li className="ml-1 pl-1 border-l-2 border-indigo-100 my-2" {...props} />
                    ),
                    // Make strong text more prominent
                    strong: ({ node, ...props }) => (
                      <strong className="font-semibold text-indigo-700" {...props} />
                    ),
                    // Enhance headings
                    h1: ({ node, ...props }) => (
                      <h1 className="text-xl font-bold text-indigo-900 mt-4 mb-2" {...props} />
                    ),
                    h2: ({ node, ...props }) => (
                      <h2 className="text-lg font-bold text-indigo-800 mt-3 mb-2" {...props} />
                    ),
                    h3: ({ node, ...props }) => (
                      <h3 className="text-base font-bold text-indigo-700 mt-2 mb-1" {...props} />
                    ),
                    // Add spacing between paragraphs
                    p: ({ node, ...props }) => (
                      <p className="my-2 leading-relaxed" {...props} />
                    ),
                  }}
                >
                  {processedContent}
                </ReactMarkdown>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
