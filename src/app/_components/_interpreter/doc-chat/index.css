@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Fix iOS overscroll behavior */
  .overscroll-none {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
  }

  /* Causal graph related styles */
  .causal-bar {
    @apply h-2 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full;
    animation: expandWidth 1.2s cubic-bezier(0.3, 0.1, 0.3, 1) forwards;
    transform-origin: left;
    box-shadow: 0 1px 4px rgba(99, 102, 241, 0.3);
  }

  @keyframes expandWidth {
    from {
      transform: scaleX(0);
    }
    to {
      transform: scaleX(1);
    }
  }

  .animate-slideDown {
    animation: slideDown 0.5s ease-out forwards;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
    to {
      opacity: 1;
      transform: translateY(0);
      max-height: 500px;
    }
  }

  /* Custom scrollbar styles */
  .styled-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .styled-scrollbar::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.8);
    border-radius: 4px;
  }

  .styled-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(203, 213, 225, 0.8);
    border-radius: 4px;
  }

  .styled-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.9);
  }

  /* Chat message formatting */
  .chat-message-user {
    @apply bg-gradient-to-r from-blue-50 to-blue-50/50 rounded-r-xl rounded-bl-xl shadow-sm border-l-2 border-blue-400;
  }

  .chat-message-assistant {
    @apply bg-gradient-to-r from-indigo-50/50 to-white rounded-l-xl rounded-br-xl shadow-sm border-l-2 border-indigo-400;
  }

  /* Glass-like UI elements */
  .glass-effect {
    @apply backdrop-blur-sm bg-white/80 border border-white/40 shadow-sm;
  }

  .glass-card {
    @apply backdrop-blur-md bg-white/90 border border-gray-100 shadow-md rounded-xl;
  }

  /* Text gradient */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-blue-600;
  }

  /* Button effects */
  .button-glow {
    transition: all 0.3s ease;
    box-shadow: 0 0 0 rgba(99, 102, 241, 0);
  }

  .button-glow:hover {
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
  }

  /* Advanced animation utilities */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slideInRight {
    animation: slideInRight 0.4s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.4s ease-out;
  }

  .animate-pulseGlow {
    animation: pulseGlow 2s infinite;
  }

  /* Animation keyframes */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulseGlow {
    0% {
      box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    @apply h-full w-full m-0 p-0;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  /* Consistent font sizing across devices */
  html {
    -webkit-text-size-adjust: 100%;
  }

  /* Improve mobile rendering */
  @media (max-width: 640px) {
    .text-base {
      font-size: 15px;
    }
  }

  /* Better markdown rendering */
  .prose pre {
    @apply bg-slate-50 p-4 rounded-md overflow-x-auto;
  }

  .prose code {
    @apply bg-slate-50 px-1 py-0.5 rounded text-sm;
  }

  .prose table {
    @apply border-collapse w-full my-4;
  }

  .prose th,
  .prose td {
    @apply border border-gray-300 p-2 text-left;
  }

  .prose th {
    @apply bg-slate-50;
  }
}
