import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronRight, BarChart4 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CausalGraphProps {
  experimentData: any;
  visible: boolean;
}

export default function CausalGraph({
  experimentData,
  visible,
}: CausalGraphProps) {
  const [expanded, setExpanded] = useState(false);
  const [factors, setFactors] = useState<
    { name: string; impact: number; significance: string }[]
  >([]);

  // Process experiment data to extract causal factors
  useEffect(() => {
    if (experimentData && experimentData.analytics_output_data) {
      try {
        // Extract AMCE data from analytics if available
        const analyticsData = experimentData.analytics_output_data;
        const extractedFactors = [];

        // Process coefficients if available
        if (analyticsData.coefficients) {
          for (const [name, value] of Object.entries<any>(
            analyticsData.coefficients
          )) {
            if (name !== "Intercept" && name !== "_intercept") {
              // Determine significance (* for p < 0.05, ** for p < 0.01, *** for p < 0.001)
              let significance = "";
              const pValue = value.p_value || 0.5; // Default if not available

              if (pValue < 0.001) significance = "***";
              else if (pValue < 0.01) significance = "**";
              else if (pValue < 0.05) significance = "*";

              extractedFactors.push({
                name: name
                  .replace(/_/g, " ")
                  .replace(/\b\w/g, (c) => c.toUpperCase()),
                impact: Math.abs(value.coefficient || value.effect || 0),
                significance,
              });
            }
          }
        }
        // If no coefficients, try to find another structure
        else if (analyticsData.average_effects) {
          for (const [name, value] of Object.entries<any>(
            analyticsData.average_effects
          )) {
            extractedFactors.push({
              name: name
                .replace(/_/g, " ")
                .replace(/\b\w/g, (c) => c.toUpperCase()),
              impact: Math.abs(value),
              significance: "",
            });
          }
        }

        // Sort by impact (highest first)
        extractedFactors.sort((a, b) => b.impact - a.impact);

        setFactors(extractedFactors.slice(0, 6)); // Show top 6 factors
      } catch (error) {
        console.error("Error processing experiment data for graph:", error);
        setFactors([]);
      }
    }
  }, [experimentData]);

  if (!visible || factors.length === 0) return null;

  const maxImpact = Math.max(...factors.map((f) => f.impact));

  return (
    <div
      className={cn(
        "w-full max-w-3xl mx-auto mb-6 overflow-hidden transition-all duration-300",
        "glass-card p-4 rounded-xl border border-indigo-100/50 hover:shadow-lg",
        !expanded ? "max-h-16" : "max-h-[400px]"
      )}
    >
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center gap-2">
          <div
            className={cn(
              "rounded-full p-1.5 transition-colors",
              expanded ? "bg-indigo-100 text-indigo-600" : "text-indigo-500"
            )}
          >
            <BarChart4 className="h-5 w-5" />
          </div>
          <h3 className="font-medium text-gradient">Causal Factor Analysis</h3>
          <div className="text-xs bg-indigo-100 text-indigo-700 px-2 py-0.5 rounded-full font-medium ml-2">
            {factors.length} factors
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "p-1 h-6 w-6 transition-transform duration-300",
            expanded ? "rotate-180" : "rotate-0"
          )}
        >
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>

      {expanded && (
        <div className="mt-4 animate-slideDown">
          <p className="text-xs text-gray-500 mb-3">
            Visualizing the relative causal impact of key factors:
          </p>
          <div className="space-y-3">
            {factors.map((factor, i) => (
              <div key={i} className="flex flex-col">
                <div className="flex items-center justify-between text-xs mb-1">
                  <span className="font-medium text-gray-700">
                    {factor.name}
                    <span className="text-indigo-700 ml-1 font-semibold">
                      {factor.significance}
                    </span>
                  </span>
                  <span className="text-gray-500 font-mono">
                    {factor.impact.toFixed(3)}
                  </span>
                </div>
                <div className="h-2 bg-gray-100 rounded-full overflow-hidden w-full">
                  <div
                    className="causal-bar h-full"
                    style={{
                      width: `${(factor.impact / maxImpact) * 100}%`,
                      animationDelay: `${i * 0.1}s`,
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 pt-3 border-t border-gray-100">
            <p className="text-xs text-gray-500">
              * p &lt; 0.05 &nbsp; ** p &lt; 0.01 &nbsp; *** p &lt; 0.001
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
