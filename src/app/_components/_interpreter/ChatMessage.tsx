import { ChatMessage as ChatMessageType } from "@/lib/api-types";
import { cn } from "@/lib/utils";
import ReactMarkdown from "react-markdown";
import { User, Bot, Download } from "lucide-react";
import { useEffect, useState, useMemo } from "react";
import { Button } from "@/components/ui/button";

interface ChatMessageProps {
  message: ChatMessageType;
  index?: number;
}

export default function ChatMessage({ message, index = 0 }: ChatMessageProps) {
  const isUser = message.role === "user";
  const [showAnimation, setShowAnimation] = useState(true);
  const isFirstAssistantMessage = !isUser && index === 1; // First assistant message is usually index 1

  // Disable the fade-in animation after it plays once
  useEffect(() => {
    const timer = setTimeout(() => setShowAnimation(false), 500);
    return () => clearTimeout(timer);
  }, []);

  // Function to download the report as a markdown file
  const downloadReport = () => {
    if (!message.content) return;

    // Create a blob with the content
    const blob = new Blob([message.content], { type: "text/plain" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `Causal_Report.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  // Process content to improve rendering
  const processedContent = useMemo(() => {
    if (isUser) return message.content;

    let content = message.content;

    // Remove duplicate "Causal Insights Report" headings
    const causalInsightsHeaders = [
      /^# CAUSAL INSIGHTS REPORT/im,
      /^# Causal Insights Report/im,
      /^## CAUSAL INSIGHTS REPORT/im,
      /^## Causal Insights Report/im,
    ];

    // Keep only the first occurrence of "Causal Insights Report" header
    let foundHeader = false;

    for (const pattern of causalInsightsHeaders) {
      if (pattern.test(content)) {
        if (foundHeader) {
          // Replace all subsequent occurrences with empty string
          content = content.replace(pattern, "");
        } else {
          // Format the first occurrence consistently
          content = content.replace(pattern, "## Causal Insights Report");
          foundHeader = true;
        }
      }
    }

    // Clean up any multiple consecutive newlines that might result from removals
    content = content.replace(/\n{3,}/g, "\n\n");

    return content;
  }, [isUser, message.content]);

  return (
    <div
      className={cn(
        "py-5 first:pt-0 w-full transition-colors",
        isUser ? "chat-message-user" : "chat-message-assistant",
        isUser ? "animate-slideInRight" : "animate-slideInLeft",
        showAnimation && "animate-fadeIn"
      )}
    >
      <div className="flex gap-4 w-full max-w-3xl mx-auto py-2 px-3 rounded-lg transition-all">
        <div
          className={cn(
            "flex-shrink-0 w-9 h-9 rounded-full flex items-center justify-center mt-1 shadow-md",
            isUser
              ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white"
              : "bg-gradient-to-br from-indigo-500 to-indigo-700 text-white",
            isUser ? "" : "animate-pulseGlow"
          )}
        >
          {isUser ? <User className="h-5 w-5" /> : <Bot className="h-5 w-5" />}
        </div>

        <div className="flex-1 min-w-0 pt-0.5">
          <div
            className={cn(
              "text-xs mb-1 font-medium flex items-center gap-1.5",
              isUser ? "text-blue-700" : "text-indigo-700"
            )}
          >
            {isUser ? (
              <>
                <span>You</span>
              </>
            ) : (
              <>
                <span>Causal Insights AI</span>
                <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              </>
            )}
          </div>

          {isUser ? (
            <p className="text-gray-800 leading-relaxed break-words">
              {processedContent}
            </p>
          ) : (
            <div className="relative">
              {/* Download button for first assistant message */}
              {isFirstAssistantMessage && (
                <div className="absolute right-0 top-0 flex items-center -mt-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={downloadReport}
                    title="Download Causal Report"
                    className="flex items-center space-x-1 text-xs text-indigo-600 hover:text-indigo-800 rounded-md py-1 px-2 bg-indigo-50 hover:bg-indigo-100 transition-colors"
                  >
                    <Download className="h-3 w-3" />
                    <span>Download Report</span>
                  </Button>
                </div>
              )}

              <div className="text-gray-800 prose prose-slate max-w-none prose-p:my-2 prose-headings:mb-3 prose-headings:mt-6 prose-headings:text-indigo-900 prose-h1:text-xl prose-h2:text-lg prose-h3:text-base prose-li:my-0.5 prose-strong:text-indigo-700 break-words prose-pre:bg-gray-50 prose-pre:border prose-pre:border-gray-200 prose-pre:rounded-md prose-pre:p-2 prose-code:text-indigo-600 prose-code:bg-indigo-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded-sm prose-code:before:content-none prose-code:after:content-none styled-scrollbar">
                <ReactMarkdown>{processedContent}</ReactMarkdown>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
