import { Alert<PERSON><PERSON>gle, LockKeyhole, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DataAccessErrorProps {
  message: string;
  onRetry?: () => void;
}

export default function DataAccessError({
  message,
  onRetry,
}: DataAccessErrorProps) {
  return (
    <div className="w-full max-w-3xl mx-auto mt-4 mb-6 overflow-hidden glass-card p-6 border border-amber-200">
      <div className="flex flex-col items-center text-center">
        <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
          <AlertTriangle className="h-6 w-6 text-amber-600" />
        </div>

        <h3 className="text-lg font-medium text-amber-800 mb-2">
          Data Access Issue
        </h3>

        <p className="text-gray-600 mb-4 max-w-md">
          {message ||
            "There was an issue accessing the experiment data. This could be due to permission issues or the data being unavailable."}
        </p>

        <div className="flex flex-col sm:flex-row gap-3 mt-2">
          {onRetry && (
            <Button
              variant="outline"
              onClick={onRetry}
              className="flex items-center gap-2 border-amber-300 text-amber-700 hover:bg-amber-50"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Retry</span>
            </Button>
          )}

          <Button
            variant="outline"
            className="flex items-center gap-2 border-amber-300 text-amber-700 hover:bg-amber-50"
            onClick={() => (window.location.href = "/")}
          >
            <LockKeyhole className="h-4 w-4" />
            <span>Return to Home</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
