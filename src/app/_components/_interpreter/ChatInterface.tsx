import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, Info, Search, Download } from "lucide-react";
import { useChat } from "@/hooks/use-chat";
import { ArtifactResponse } from "@/lib/api-types";
import ChatMessage from "./ChatMessage";
import CausalGraph from "./CausalGraph";
import InsightsSummary from "./InsightsSummary";
import DataAccessError from "./DataAccessError";

interface ChatInterfaceProps {
  experimentData: ArtifactResponse | null;
  reportText: string;
  runId: string;
  summaryVisible: boolean;
  toggleSummary: () => void;
  toggleSidebar: () => void; // Keeping this to maintain compatibility, but it won't be used
}

export default function ChatInterface({
  experimentData,
  reportText,
  runId,
  summaryVisible,
  toggleSummary,
  toggleSidebar, // Keep for interface compatibility
}: ChatInterfaceProps) {
  const [userInput, setUserInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const [showGraph, setShowGraph] = useState(false);
  const [showInsights, setShowInsights] = useState(false);

  const { messages, isLoading, sendMessage } = useChat(runId, reportText);

  // Scroll to bottom of messages when messages update
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // State to track data access errors
  const [dataAccessError, setDataAccessError] = useState<string | null>(null);

  // Show graph and insights automatically once data is loaded
  useEffect(() => {
    if (experimentData && (!showGraph || !showInsights)) {
      // Only show visualizations if we have experiment data
      if (!showGraph) setShowGraph(true);
      if (!showInsights) setShowInsights(true);
    }
  }, [experimentData, showGraph, showInsights]);

  // Detect data access errors in report text or experiment data
  useEffect(() => {
    if (
      reportText &&
      reportText.includes("system error") &&
      reportText.includes("file is inaccessible")
    ) {
      setDataAccessError(
        "The experiment definition file is inaccessible. You may not have permission to access this data."
      );
    } else if (
      experimentData?.success === true &&
      (!experimentData.experiment_definition_data ||
        Object.keys(experimentData.experiment_definition_data).length === 0)
    ) {
      setDataAccessError(
        "Unable to retrieve complete experiment data. This may be due to permission issues."
      );
    } else {
      setDataAccessError(null);
    }
  }, [reportText, experimentData]);

  // Auto resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "inherit";
      textareaRef.current.style.height = `${Math.min(
        textareaRef.current.scrollHeight,
        150
      )}px`;
    }
  }, [userInput]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (userInput.trim() && !isLoading) {
      sendMessage(userInput);
      setUserInput("");

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = "inherit";
      }
    }
  };

  const getExperimentTitle = () => {
    // Try to get a more descriptive title, which may be in different places
    let title = experimentData?.experiment_definition_data?.title || "";

    // If no title found, check in report text for title in different formats
    if (reportText && (!title || title.includes("_"))) {
      // First try to extract from "CAUSAL INSIGHTS REPORT: Title" format
      const titleFromHeader = reportText.match(
        /# (?:CAUSAL INSIGHTS REPORT|Causal Insights Report):\s*(.*?)(?:\n|$)/i
      );
      if (titleFromHeader && titleFromHeader[1]) {
        title = titleFromHeader[1].trim();
      } else {
        // Then try to find "What..." patterns which often contain the full title
        const matchTitle = reportText.match(/What[\w\s\d,'":()]+?(?=[.?!])/i);
        if (matchTitle && matchTitle[0]) {
          title = matchTitle[0].trim();
        }
      }
    }

    // If still no good title, use a default
    if (!title) {
      title = "Causal Insights Analysis";
    }

    // Format the title by replacing underscores with spaces and capitalizing words
    title = title
      .replace(/_/g, " ")
      .split(" ")
      .map(
        (word: string) =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      )
      .join(" ");

    return title;
  };

  // Function to download the causal report as a text file
  const downloadCausalReport = () => {
    if (!reportText) return;

    // Create a blob with the report text
    const blob = new Blob([reportText], { type: "text/plain" });
    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);
    // Create a link element
    const a = document.createElement("a");
    // Set the link's attributes
    a.href = url;
    a.download = `Causal_Report_${runId.substring(0, 8)}.md`;
    // Append the link to the document
    document.body.appendChild(a);
    // Click the link to trigger the download
    a.click();
    // Remove the link from the document
    document.body.removeChild(a);
    // Release the blob URL
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="flex flex-col min-h-screen w-full">
      {/* Header - Fixed at top */}
      <header className="bg-gradient-to-r from-indigo-50 via-blue-50 to-indigo-50 border-b border-gray-200 py-4 px-4 sm:px-6 sticky top-0 z-10 w-full shadow-lg backdrop-blur-md bg-white/70">
        <div className="flex items-center justify-between w-full max-w-3xl mx-auto">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-br from-indigo-500 to-blue-600 w-11 h-11 rounded-xl flex items-center justify-center shadow-md animate-pulseGlow">
              <div className="text-white font-bold text-base">CI</div>
            </div>
            <div className="flex flex-col">
              <h1
                className="text-lg font-semibold text-gradient inline-block max-w-[240px] sm:max-w-[400px] overflow-hidden text-ellipsis whitespace-nowrap"
                title={getExperimentTitle()}
              >
                {getExperimentTitle()}
              </h1>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-1.5 shadow-sm animate-pulse"></div>
                <p className="text-xs font-medium text-indigo-700/80">
                  Causal Insights Analysis
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Download Report Button */}
            {reportText && (
              <Button
                variant="ghost"
                size="icon"
                onClick={downloadCausalReport}
                title="Download Causal Report"
                className="rounded-full h-9 w-9 flex-shrink-0 hover:bg-indigo-100 transition-colors button-glow"
              >
                <Download className="h-4.5 w-4.5 text-indigo-700" />
              </Button>
            )}

            {/* Info Button */}
            <div className="group">
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full h-9 w-9 flex-shrink-0 hover:bg-indigo-100 transition-colors button-glow"
              >
                <Info className="h-4.5 w-4.5 text-indigo-700" />
              </Button>

              <div className="absolute right-0 top-full mt-2 w-80 max-w-[90vw] rounded-lg glass-card p-4 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-20 animate-fadeIn">
                <div className="flex items-center mb-2">
                  <div className="w-1 h-5 bg-gradient-to-b from-indigo-600 to-blue-600 rounded-full mr-2"></div>
                  <p className="font-medium text-gradient">
                    Causal Insights Platform
                  </p>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  This AI-powered assistant analyzes experiment data to provide
                  causal insights. Ask questions about the experiment results,
                  methodology, or specific findings to understand what factors
                  have the strongest impact.
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Chat Messages Container - Taking remaining height */}
      <div
        ref={chatContainerRef}
        className="flex-grow w-full px-4 sm:px-6 py-6 mb-20"
      >
        <div className="w-full max-w-3xl mx-auto">
          {/* Data Access Error Display */}
          {dataAccessError && messages.length > 0 && (
            <DataAccessError
              message={dataAccessError}
              onRetry={() => window.location.reload()}
            />
          )}

          {/* Structured Insights Summary - only show if data is loaded and no error */}
          <InsightsSummary
            experimentData={experimentData}
            reportText={reportText}
            visible={showInsights && messages.length > 0 && !dataAccessError}
          />

          {/* Causal graph - only show if data is loaded and no error */}
          <CausalGraph
            experimentData={experimentData}
            visible={showGraph && messages.length > 0 && !dataAccessError}
          />

          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-96 mt-8">
              <div className="relative mb-8">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 flex items-center justify-center shadow-lg animate-pulseGlow">
                  <div className="absolute inset-0 rounded-full border-t-4 border-l-4 border-white/30 animate-spin"></div>
                  <div className="h-10 w-10 rounded-full glass-effect flex items-center justify-center">
                    <div className="text-gradient font-semibold">CI</div>
                  </div>
                </div>
              </div>
              <h3 className="text-gradient font-semibold text-lg mb-2">
                Processing Experiment Data
              </h3>
              <p className="text-gray-600 text-center max-w-md mb-4">
                Analyzing causal relationships and preparing insights based on
                your experiment data.
              </p>
              <div className="flex space-x-2 items-center">
                <div className="h-2 w-2 bg-indigo-400 rounded-full animate-ping"></div>
                <div
                  className="h-2 w-2 bg-indigo-500 rounded-full animate-ping"
                  style={{ animationDelay: "0.2s" }}
                ></div>
                <div
                  className="h-2 w-2 bg-indigo-600 rounded-full animate-ping"
                  style={{ animationDelay: "0.4s" }}
                ></div>
              </div>
            </div>
          ) : (
            messages.map((message, index) => (
              <ChatMessage key={index} message={message} index={index} />
            ))
          )}

          {/* Auto-scroll anchor */}
          <div ref={messagesEndRef} />

          {/* Loading indicator */}
          {isLoading && (
            <div className="py-5 flex justify-center animate-fadeIn">
              <div className="flex items-center space-x-4 glass-card px-5 py-3">
                <div className="relative">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 flex items-center justify-center shadow-md animate-pulseGlow">
                    <div className="absolute inset-0 rounded-full border-t-2 border-indigo-200 animate-spin"></div>
                    <div className="h-4 w-4 rounded-full bg-white"></div>
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="text-sm font-medium text-gradient">
                    Analyzing causal data...
                  </div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    Identifying key relationships and insights
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chat Input - Fixed at bottom */}
      <div className="bg-gradient-to-r from-indigo-50 via-blue-50 to-indigo-50 border-t border-gray-200 py-4 px-4 sm:px-6 fixed bottom-0 z-10 w-full shadow-[0_-2px_10px_rgba(0,0,0,0.05)] backdrop-blur-md">
        <div className="w-full max-w-3xl mx-auto">
          <form
            onSubmit={handleSubmit}
            className="flex items-end gap-2 glass-card p-3 transition-all focus-within:shadow-md focus-within:border-indigo-300 hover:shadow-lg"
          >
            <div className="flex-1 min-w-0 relative">
              <Textarea
                ref={textareaRef}
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="Ask about causal effects, experiment details, or recommendations..."
                className="resize-none min-h-[24px] max-h-[150px] overflow-y-auto border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-2 w-full text-gray-800 placeholder:text-gray-400 styled-scrollbar"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
                disabled={isLoading}
              />
              {!userInput && (
                <div className="absolute left-3 bottom-2 text-xs text-gray-400/80 pointer-events-none">
                  <div className="flex items-center">
                    <Search className="w-3 h-3 mr-1.5 text-indigo-400" />
                    <span>Type a question to analyze causal relationships</span>
                  </div>
                </div>
              )}
            </div>
            <Button
              type="submit"
              disabled={!userInput.trim() || isLoading}
              size="icon"
              className={`rounded-full p-2 h-11 w-11 flex-shrink-0 bg-gradient-to-br from-indigo-500 to-blue-600 shadow-md transition-all button-glow ${
                !userInput.trim() || isLoading
                  ? "opacity-50"
                  : "hover:opacity-90"
              }`}
            >
              <Send className="h-5 w-5 text-white" />
            </Button>
          </form>
          <div className="flex justify-center mt-2">
            <div className="text-xs text-indigo-700/70 font-medium glass-effect px-4 py-1.5 rounded-full shadow-sm">
              Press{" "}
              <kbd className="px-1.5 py-0.5 text-xs bg-white/80 border border-indigo-200 rounded-md mx-1 font-mono shadow-sm">
                Enter
              </kbd>{" "}
              to send,{" "}
              <kbd className="px-1.5 py-0.5 text-xs bg-white/80 border border-indigo-200 rounded-md mx-1 font-mono shadow-sm">
                Shift+Enter
              </kbd>{" "}
              for a new line
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
