import { useState } from "react";
import { cn } from "@/lib/utils";
import {
  <PERSON>bulb,
  TrendingUp,
  TrendingDown,
  ChevronDown,
  ChevronRight,
  Users,
  PieChart,
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface InsightsSummaryProps {
  experimentData: any;
  reportText: string;
  visible: boolean;
}

export default function InsightsSummary({
  experimentData,
  reportText,
  visible,
}: InsightsSummaryProps) {
  const [expanded, setExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "surprising" | "positive" | "negative" | "segments"
  >("surprising");

  if (!visible) return null;

  // Helper function to extract insights from report text
  const extractInsights = () => {
    const insights = {
      surprising: [] as { title: string; description: string }[],
      positive: [] as { factor: string; impact: string }[],
      negative: [] as { factor: string; impact: string }[],
      segments: [] as { segment: string; insight: string }[],
    };

    try {
      // Extract surprising findings
      const surprisingRegex = /surprising|unexpected|interesting|notable/i;
      const sentences = reportText.split(/[.!?]\s+/);

      for (let i = 0; i < sentences.length; i++) {
        const sentence = sentences[i];
        if (surprisingRegex.test(sentence) && i < sentences.length - 1) {
          const nextSentence = sentences[i + 1];
          insights.surprising.push({
            title: sentence.trim().replace(/^[,\s]+/, ""),
            description: nextSentence.trim(),
          });
        }
      }

      // Extract positive impacts
      const positiveRegex =
        /positive impact|strong positive|increased|improved/i;
      for (const sentence of sentences) {
        const match = sentence.match(/([^,]+) had a ([^.]+) positive impact/i);
        if (match || positiveRegex.test(sentence)) {
          const factor = match
            ? match[1].trim()
            : sentence.split(/had|showed|demonstrated/i)[0]?.trim();
          const impact = match
            ? match[2].trim()
            : sentence.split(/impact|effect/i)[1]?.trim() || "positive impact";

          if (
            factor &&
            impact &&
            !factor.includes("negative") &&
            factor.length > 5
          ) {
            insights.positive.push({ factor, impact });
          }
        }
      }

      // Extract negative impacts
      const negativeRegex = /negative impact|decreased|reduced|lowered/i;
      for (const sentence of sentences) {
        const match = sentence.match(/([^,]+) had a ([^.]+) negative impact/i);
        if (match || negativeRegex.test(sentence)) {
          const factor = match
            ? match[1].trim()
            : sentence.split(/had|showed|demonstrated/i)[0]?.trim();
          const impact = match
            ? match[2].trim()
            : sentence.split(/impact|effect/i)[1]?.trim() || "negative impact";

          if (
            factor &&
            impact &&
            !factor.includes("positive") &&
            factor.length > 5
          ) {
            insights.negative.push({ factor, impact });
          }
        }
      }

      // Extract segment-specific insights
      const segmentRegex =
        /segment|demographic|audience|group|user type|user profile/i;
      for (const sentence of sentences) {
        if (segmentRegex.test(sentence)) {
          const segmentMatch = sentence.match(/"([^"]+)"|"([^"]+)"/);
          const segment = segmentMatch
            ? segmentMatch[1] || segmentMatch[2]
            : sentence.split(/showed|demonstrated|had/i)[0]?.trim();

          if (segment && segment.length > 5) {
            insights.segments.push({
              segment,
              insight: sentence.trim(),
            });
          }
        }
      }

      // Limit to top 5 of each
      insights.surprising = insights.surprising.slice(0, 5);
      insights.positive = insights.positive.slice(0, 5);
      insights.negative = insights.negative.slice(0, 5);
      insights.segments = insights.segments.slice(0, 5);

      // If any sections are empty, generate defaults
      if (insights.surprising.length === 0) {
        insights.surprising.push({
          title: "Significant variation in user preferences",
          description:
            "Different user segments showed notably different preferences for the same features",
        });
      }

      return insights;
    } catch (error) {
      console.error("Error processing insights:", error);
      return {
        surprising: [
          {
            title: "Error processing insights",
            description: "Could not extract insights from report",
          },
        ],
        positive: [],
        negative: [],
        segments: [],
      };
    }
  };

  const insights = extractInsights();

  return (
    <div
      className={cn(
        "w-full max-w-3xl mx-auto mb-6 overflow-hidden transition-all duration-300",
        "glass-card p-4 rounded-xl border border-indigo-100/50 hover:shadow-lg",
        !expanded ? "max-h-16" : "max-h-[550px]"
      )}
    >
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center gap-2">
          <div
            className={cn(
              "rounded-full p-1.5 transition-colors",
              expanded ? "bg-indigo-100 text-indigo-600" : "text-indigo-500"
            )}
          >
            <Lightbulb className="h-5 w-5" />
          </div>
          <h3 className="font-medium text-gradient">Key Causal Insights</h3>
          <div className="text-xs bg-indigo-100 text-indigo-700 px-2 py-0.5 rounded-full font-medium ml-2">
            Structured Analysis
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "p-1 h-6 w-6 transition-transform duration-300",
            expanded ? "rotate-180" : "rotate-0"
          )}
        >
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>

      {expanded && (
        <div className="mt-4 animate-slideDown">
          {/* Tabs */}
          <div className="flex justify-center mb-4 flex-wrap">
            <div className="flex bg-slate-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveTab("surprising")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "surprising"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <Lightbulb className="h-3 w-3" />
                <span>Surprising</span>
              </button>
              <button
                onClick={() => setActiveTab("positive")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "positive"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <TrendingUp className="h-3 w-3" />
                <span>Positive</span>
              </button>
              <button
                onClick={() => setActiveTab("negative")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "negative"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <TrendingDown className="h-3 w-3" />
                <span>Negative</span>
              </button>
              <button
                onClick={() => setActiveTab("segments")}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
                  activeTab === "segments"
                    ? "bg-white shadow-sm text-indigo-700"
                    : "text-slate-600 hover:text-indigo-600"
                )}
              >
                <Users className="h-3 w-3" />
                <span>Segments</span>
              </button>
            </div>
          </div>

          {/* Content based on active tab */}
          <div className="p-1">
            {activeTab === "surprising" && (
              <div className="space-y-3">
                {insights.surprising.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-indigo-50/30 border border-indigo-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-indigo-100 p-1.5 mt-0.5">
                        <Lightbulb className="h-3.5 w-3.5 text-indigo-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-indigo-900">
                          {item.title}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "positive" && (
              <div className="space-y-3">
                {insights.positive.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-green-50/30 border border-green-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-green-100 p-1.5 mt-0.5">
                        <TrendingUp className="h-3.5 w-3.5 text-green-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-green-900">
                          {item.factor}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          Impact: {item.impact}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "negative" && (
              <div className="space-y-3">
                {insights.negative.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-red-50/30 border border-red-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-red-100 p-1.5 mt-0.5">
                        <TrendingDown className="h-3.5 w-3.5 text-red-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-red-900">
                          {item.factor}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          Impact: {item.impact}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "segments" && (
              <div className="space-y-3">
                {insights.segments.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg bg-gradient-to-r from-white to-blue-50/30 border border-blue-100/50"
                  >
                    <div className="flex items-start gap-2">
                      <div className="rounded-full bg-blue-100 p-1.5 mt-0.5">
                        <Users className="h-3.5 w-3.5 text-blue-700" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-blue-900">
                          {item.segment}
                        </h4>
                        <p className="text-xs text-gray-600 mt-1">
                          {item.insight}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
            <p className="text-xs text-gray-500">
              Insights automatically extracted from causal analysis
            </p>
            <div className="text-xs bg-indigo-100 text-indigo-600 px-2 py-1 rounded-full font-medium">
              AI-powered
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
