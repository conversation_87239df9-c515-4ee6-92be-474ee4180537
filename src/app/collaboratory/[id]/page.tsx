"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/app/lib/firebase";
import CollaborativeEditor from "@/app/_components/_collaboratory/CollaborativeEditor";
import ShareModal from "@/app/_components/_collaboratory/ShareModal";
import { useUser } from "@auth0/nextjs-auth0/client";
import { Room } from "./Room";
import RightSidebar from "@/app/_components/_collaboratory/RightSidebar";
import Loading from "@/app/_components/_ui/Loading";

interface Collaborator {
  nickname: string;
  email: string;
  role: string;
}

interface Experiment {
  id: string;
  name: string;
  runName?: string;
  collaborators: Collaborator[];
  artifactDetails?: Record<string, any>;
}

export default function CollaboratoryExperimentPage() {
  const params = useParams();
  const id = params.id as string;

  const [experiment, setExperiment] = useState<Experiment | null>(null);
  const [isRoomReady, setIsRoomReady] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [artifactContent, setArtifactContent] = useState<string | null>(null);
  const { user, isLoading } = useUser();

  useEffect(() => {
    if (!id || !user) return;

    const fetchExperiment = async () => {
      const docRef = doc(db, "collaboratories", id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();
        const collaboratorsWithNicknames = (data.collaborators || []).map(
          (collab: { email: string; role: string }) => ({
            email: collab.email,
            role: collab.role,
            nickname: collab.email.split("@")[0],
          })
        );

        setExperiment({
          id,
          name: data.name,
          runName: data.runName || "",
          collaborators: collaboratorsWithNicknames,
          artifactDetails: data.artifactDetails || {},
        });
      } else {
        setExperiment(null);
      }

      setTimeout(() => setIsRoomReady(true), 500);
    };

    fetchExperiment();
  }, [id, user]);

  useEffect(() => {
    const fetchArtifact = async () => {
      if (!experiment?.runName) return;

      const file_name = `experiment_research_report_${experiment.runName}`;
      try {
        const response = await fetch(`/api/runs/artifact/${file_name}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Error fetching artifact:", errorData);
          return;
        }

        const blob = await response.blob();
        const text = await blob.text();
        setArtifactContent(text);
      } catch (error) {
        console.error("Error fetching artifact:", error);
        setArtifactContent(null);
      }
    };

    fetchArtifact();
  }, [experiment?.runName]);

  if (isLoading || !isRoomReady) return <Loading />;
  if (!user) return <div>Please log in to view this page.</div>;
  if (!experiment) return <div>Experiment not found.</div>;

  return (
    <div className="bg-[#f4f4f5] pb-8 w-full min-h-screen flex flex-col font-inter relative">
      <div className="px-10 py-4 bg-white flex justify-between items-center">
        <h1 className="text-2xl">{experiment.name}</h1>
        <button
          className="bg-[#312E81] text-white px-4 py-2 rounded"
          onClick={() => setIsShareModalOpen(true)}
        >
          Share
        </button>
      </div>

      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        experimentId={experiment.id}
        experimentTitle={experiment.name}
        owner={{ nickname: user.nickname || "", email: user.email || "" }}
        collaborators={experiment.collaborators || []}
        onUpdateCollaborators={() => {}}
      />

      {isRoomReady && (
        <Room>
          <CollaborativeEditor
            experimentId={experiment.id}
            runName={experiment.runName}
            artifactDetails={experiment.artifactDetails || {}}
            artifactContent={artifactContent} // Pass the new artifact content
            isReadOnly={
              experiment.collaborators.find((c) => c.email === user.email)
                ?.role === "Can view"
            }
          />
        </Room>
      )}

      <RightSidebar experimentId={experiment.id} />
    </div>
  );
}
