@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,600;1,400;1,600&display=swap");
@import "./_components/_interpreter/doc-chat/index.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

:root {
  --color-black: 0 0 0;
  --color-white: 255 255 255;
  --color-gray-50: 250 250 250;
  --color-gray-100: 244 244 245;
  --color-gray-150: 236 236 238;
  --color-gray-200: 228 228 231;
  --color-gray-250: 220 220 224;
  --color-gray-300: 212 212 216;
  --color-gray-350: 187 187 193;
  --color-gray-400: 161 161 170;
  --color-gray-450: 137 137 146;
  --color-gray-500: 113 113 122;
  --color-gray-550: 98 98 107;
  --color-gray-600: 82 82 91;
  --color-gray-650: 73 73 81;
  --color-gray-700: 63 63 70;
  --color-gray-750: 51 51 56;
  --color-gray-800: 39 39 42;
  --color-gray-850: 32 32 35;
  --color-gray-900: 24 24 27;
  --color-gray-950: 18 18 21;
  --color-overlay: rgb(var(--color-black) / 60%);
  --space-0: 0;
  --space-1: 0.125rem;
  --space-2: 0.25rem;
  --space-3: 0.375rem;
  --space-4: 0.5rem;
  --space-5: 0.625rem;
  --space-6: 0.75rem;
  --space-7: 0.875rem;
  --space-8: 1rem;
  --space-9: 1.25rem;
  --space-10: 1.5rem;
  --space-11: 1.75rem;
  --space-12: 2rem;
  --space-13: 2.25rem;
  --space-14: 2.5rem;
  --space-15: 3rem;
  --space-16: 3.5rem;
  --space-17: 4rem;
  --space-18: 4.5rem;
  --space-19: 5rem;
  --space-20: 6rem;
  --space-21: 7rem;
  --space-22: 8rem;
  --space-23: 9rem;
  --space-24: 10rem;
  --size-2xs: 0.625rem;
  --size-xs: 0.75rem;
  --size-sm: 0.875rem;
  --size: 1rem;
  --size-lg: 1.125rem;
  --size-xl: 1.25rem;
  --size-2xl: 1.5rem;
  --size-3xl: 2rem;
  --size-4xl: 3rem;
  --radius-xs: 0.3rem;
  --radius-sm: 0.4rem;
  --radius: 0.7rem;
  --radius-lg: 0.8rem;
  --shadow-xs: 0 1px 4px rgb(var(--color-black) / 5%);
  --shadow-sm: 0 2px 8px rgb(var(--color-black) / 5%);
  --shadow: 0 3px 10px rgb(var(--color-black) / 5%);
  --shadow-lg: 0 4px 20px rgb(var(--color-black) / 5%);
  --shadow-xl: 0 5px 30px rgb(var(--color-black) / 5%);
  --backdrop-surface: saturate(2) blur(16px);
  --transition: 0.15s ease-in-out;
  --transition-linear: 0.15s linear;
  --header-height: 60px;
  --z-above: 100;
  --z-overlay: 300;
  --z-badge: 1000;
  --opacity-hover: 0.8;
  --opacity-disabled: 0.5;

  accent-color: var(--color-accent);
}

:root {
  --color-red: 239 67 67;
  --color-green: 132 204 22;
  --color-accent: rgb(var(--color-gray-900));
  --color-alternative-accent: rgb(29, 78, 216);
  --color-surface: rgb(var(--color-gray-100));
  --color-surface-hover: rgb(var(--color-gray-150));
  --color-surface-elevated: rgb(var(--color-white));
  --color-border: rgb(var(--color-gray-150));
  --color-border-contrasted: rgb(var(--color-gray-250));
  --color-border-transparent: rgb(var(--color-gray-900) / 10%);
  --color-skeleton: rgb(var(--color-gray-150));
  --color-skeleton-shine: rgb(var(--color-gray-50));
  --color-tooltip: rgb(var(--color-gray-950));
  --color-tooltip-text: rgb(var(--color-white));
  --color-tooltip-border: rgb(var(--color-gray-750));
  --color-text: rgb(var(--color-gray-900));
  --color-text-light: rgb(var(--color-gray-600));
  --color-text-lighter: rgb(var(--color-gray-500));
  --color-text-lightest: rgb(var(--color-gray-400));
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  margin: 0;
  padding: 0;
}

*,
*::after,
*::before {
  box-sizing: inherit;
}

body,
html {
  overflow: hidden;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

*,
*::after,
*::before {
  box-sizing: inherit;
}

body,
html {
  overflow-x: hidden; /* Only hide horizontal overflow */
  overflow-y: auto; /* Allow vertical scrolling */
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
}

.subscription-box {
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}

.settings-footer {
  border-top: 1px solid #eaecf0;
}

.bg-soft-gradient {
  background-image: linear-gradient(
    90deg,
    rgba(124, 226, 225, 1) 0%,
    rgba(129, 175, 250, 1) 35%,
    rgba(153, 167, 249, 1) 50%,
    rgba(182, 169, 240, 1) 75%,
    rgba(204, 146, 245, 1) 100%
  );
}

.boxshadow-allexps {
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}

.boxshadow-dropdown {
  box-shadow:
    0px 12px 16px -4px rgba(16, 24, 40, 0.08),
    0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}
.boxshadow-feeling-lucky {
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideIn {
  animation-name: slideIn;
  animation-timing-function: ease-out;
  animation-duration: 200ms;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  background-color: #2d2e61;
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #444;
  z-index: 1;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: inherit;
  font-family: inherit;
  font-weight: normal;
  width: 400px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.tooltip:hover::after {
  visibility: visible;
  opacity: 1;
  transform: translateX(3%) translateY(-50px);
}

.visualization-modal {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.8);
}
.modal-container {
  height: 100%;
  width: 100%;
  background-color: #000000;
  position: auto;
}
.visualization-wrapper {
  height: 100%;
  width: 100%;
  overflow-y: auto;
}
.close-modal {
  position: absolute;
  top: 10px;
  right: 2%;
  cursor: pointer;
  color: #ffffff;
  border-radius: 5px;
  padding: 1px 8px;
  background-color: #000000;
  border: 1px solid #ffffff;
  transition: 0.1s;
  font-size: 15px;
}
.close-modal:hover {
  background-color: #ffffff;
  border: 1px solid #000000;
  color: #000000;
}

.loading-dataset {
  position: absolute;
  top: 40%;
  min-width: 100%;
  cursor: pointer;
  color: #ffffff;
  text-align: center;
}

.tiptap {
  :first-child {
    margin-top: 0;
  }

  img {
    display: block;
    height: auto;
    margin: 1.5rem 0;
    max-width: 100%;

    &.ProseMirror-selectednode {
      outline: 3px solid blue;
    }
  }

  /* List styles */
  ul,
  ol {
    padding: 0 1rem;
    margin: 1.25rem 1rem 1.25rem 0.4rem;
  }

  ul li {
    list-style-type: disc;

    p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  ol li {
    list-style-type: decimal;

    p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }
}

h1.tiptap-heading {
  font-size: var(--size-3xl);
  font-weight: bold;
  margin-top: var(--space-12);
  margin-bottom: var(--space-8);
}

h2.tiptap-heading {
  font-size: var(--size-2xl);
  font-weight: bold;
  margin-top: var(--space-12);
  margin-bottom: var(--space-8);
}

h3.tiptap-heading {
  font-size: var(--size-xl);
  font-weight: bold;
  margin-top: var(--space-12);
  margin-bottom: var(--space-8);
}

h1.tiptap-heading:first-child,
h2.tiptap-heading:first-child,
h3.tiptap-heading:first-child {
  margin-top: 0;
}

h1.tiptap-heading + h2.tiptap-heading,
h1.tiptap-heading + h3.tiptap-heading,
h2.tiptap-heading + h1.tiptap-heading,
h2.tiptap-heading + h3.tiptap-heading,
h3.tiptap-heading + h1.tiptap-heading,
h3.tiptap-heading + h2.tiptap-heading {
  margin-top: 0;
}

.floating-threads {
  display: none;
}

/* For desktop */
.anchored-threads {
  display: block;
  max-width: 300px;
  width: 100%;
  position: absolute;
  right: 12px;
}

@media (max-width: 640px) {
  .floating-threads {
    display: block;
  }

  .anchored-threads {
    display: none;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
