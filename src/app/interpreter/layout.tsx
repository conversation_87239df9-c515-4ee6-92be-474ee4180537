"use client";
import React from "react";

interface InterpreterLayoutProps {
  children: React.ReactNode;
}

const InterpreterLayout: React.FC<InterpreterLayoutProps> = ({ children }) => {
  return (
    <div suppressHydrationWarning className="w-full h-screen bg-background">
      {/* Full-width layout without the navigation sidebar */}
      <div className="h-full overflow-auto">{children}</div>
    </div>
  );
};

export default InterpreterLayout;
