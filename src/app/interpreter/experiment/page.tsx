"use client";

import { useEffect, useState } from "react";
import { useExperiment } from "@/app/_components/_interpreter/doc-chat/hooks/use-experiment";
import ChatInterface from "@/app/_components/_interpreter/doc-chat/components/experiment/ChatInterface";
import Loading from "@/app/_components/_ui/Loading";
import { useParams, useRouter } from "next/navigation";

export default function Experiment() {
  const [runId, setRunId] = useState<string | null>(null);
  const { push } = useRouter();

  const { run_id } = useParams();

  const { experimentData, reportText, isLoading, isError, error, retry } =
    useExperiment(runId);

  useEffect(() => {
    if (run_id) {
      console.log(`Setting run ID from path parameter: ${run_id}`);
      setRunId(run_id as string);
    } else {
      push("/");
    }
  }, [run_id, push]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loading />
        <p className="text-lg mt-4">Loading Experiment Data</p>
        <p className="text-gray-500">
          Fetching experiment details and generating causal insights report...
        </p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loading />
        <p className="text-lg mt-4">Error Loading Data</p>
        <p className="text-gray-500">
          {error instanceof Error
            ? error.message
            : "An unexpected error occurred"}
        </p>
        <button
          onClick={retry}
          className="mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen w-full bg-white">
      <ChatInterface
        experimentData={experimentData}
        reportText={reportText}
        runId={runId || ""}
        summaryVisible={false}
        toggleSummary={() => {}}
        toggleSidebar={() => {}}
      />
    </div>
  );
}
