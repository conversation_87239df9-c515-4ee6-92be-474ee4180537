"use client";
import React, { useState } from "react";
import { use } from "react";
import ChatInterface from "../../_components/_interpreter/doc-chat/components/experiment/ChatInterface";
import { useExperiment } from "../../_components/_interpreter/doc-chat/hooks/use-experiment";
import { serializeData } from "@/app/utils/serialization";

const InterpreterPage = ({
  params,
}: {
  params: Promise<{ run_id: string }>;
}) => {
  // Unwrap the params Promise using React.use()
  const unwrappedParams = use(params);
  const runId = unwrappedParams.run_id;

  // State for UI controls
  const [summaryVisible, setSummaryVisible] = useState(false);

  // Fetch experiment data and report
  const { experimentData, reportText, isLoading, isError, error } =
    useExperiment(runId);

  // Toggle summary visibility
  const toggleSummary = () => {
    setSummaryVisible((prev) => !prev);
  };

  // Empty function for sidebar toggle (not used)
  const toggleSidebar = () => {};

  return (
    <div className="flex flex-col min-h-screen w-full bg-white">
      {/* Full-height interface with minimal UI */}
      <ChatInterface
        experimentData={serializeData(experimentData)}
        reportText={reportText}
        runId={runId}
        summaryVisible={summaryVisible}
        toggleSummary={toggleSummary}
        toggleSidebar={toggleSidebar}
      />
    </div>
  );
};

export default InterpreterPage;
