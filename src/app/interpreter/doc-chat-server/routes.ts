import type { Express, Request, Response, RequestHandler } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import {
  getExperimentArtifacts,
  generateCausalInsightsReport,
  processExperimentChat,
  getAuthToken,
} from "./api-integration";
import { getServerStatus } from "./python-api-server";
import {
  RunIdRequest,
  ReportRequest,
  ChatRequest,
} from "../doc-chat-types/api-types";

export async function registerRoutes(app: Express): Promise<Server> {
  // Get experiment artifacts
  const getExperimentArtifactHandler: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const { run_id } = req.body as RunIdRequest;

      if (!run_id) {
        res.status(400).json({
          success: false,
          error: "Missing required parameter: run_id",
        });
        return;
      }

      const artifactData = await getExperimentArtifacts(run_id);
      res.json(artifactData);
    } catch (error) {
      console.error("Error fetching experiment artifacts:", error);
      res.status(500).json({
        success: false,
        error: `Failed to fetch experiment artifacts: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    }
  };

  app.post("/api/get-experiment-artifact", getExperimentArtifactHandler);

  // Generate text-based causal insights report
  const generateReportHandler: RequestHandler = async (
    req: Request,
    res: Response
  ) => {
    try {
      const { run_id, format = "text" } = req.body as ReportRequest;

      if (!run_id) {
        res.status(400).json({
          success: false,
          error: "Missing required parameter: run_id",
        });
        return;
      }

      // We're focusing on text reports per requirements
      if (format !== "text") {
        res.status(400).json({
          success: false,
          error: "Only text format reports are supported",
        });
        return;
      }

      const reportData = await generateCausalInsightsReport(run_id);
      res.json(reportData);
    } catch (error) {
      console.error("Error generating causal insights report:", error);
      res.status(500).json({
        success: false,
        error: `Failed to generate report: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    }
  };

  app.post("/api/generate-report", generateReportHandler);

  // Process chat with experiment context
  const chatHandler: RequestHandler = async (req: Request, res: Response) => {
    try {
      const { run_id, messages } = req.body as ChatRequest;

      if (
        !run_id ||
        !messages ||
        !Array.isArray(messages) ||
        messages.length === 0
      ) {
        res.status(400).json({
          success: false,
          error: "Missing required parameters: run_id and/or messages",
        });
        return;
      }

      // Get the last user message from the array
      const lastUserMessage = messages[messages.length - 1];
      // Get previous messages for context (excluding the last one)
      const previousMessages = messages.slice(0, -1);

      const chatResponse = await processExperimentChat(
        run_id,
        lastUserMessage.content,
        previousMessages
      );
      res.json(chatResponse);
    } catch (error) {
      console.error("Error processing chat:", error);
      res.status(500).json({
        success: false,
        error: `Failed to process chat: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    }
  };

  app.post("/api/chat", chatHandler);

  // Test connection endpoint with real API connection status
  const testConnectionHandler: RequestHandler = async (
    _req: Request,
    res: Response
  ) => {
    try {
      // Try to get a real auth token to test authentication
      try {
        const token = await getAuthToken();

        // Get Python API server status if in local mode
        const pythonApiStatus =
          process.env.LOCAL_API_MODE === "true"
            ? getServerStatus()
            : { running: false, startupAttempts: 0, maxAttempts: 0 };

        res.json({
          status: "success",
          message: "API connection test successful",
          authenticated: !!token,
          api_url: process.env.API_BASE_URL,
          google_ai_available: !!process.env.GOOGLE_API_KEY,
          local_api_mode: process.env.LOCAL_API_MODE === "true",
          python_api_status: pythonApiStatus,
        });
      } catch (authError) {
        console.error("Auth error in test connection:", authError);

        // Get Python API server status if in local mode
        const pythonApiStatus =
          process.env.LOCAL_API_MODE === "true"
            ? getServerStatus()
            : { running: false, startupAttempts: 0, maxAttempts: 0 };

        res.json({
          status: "error",
          message: "API authentication failed",
          error:
            authError instanceof Error
              ? authError.message
              : "Unknown authentication error",
          api_url: process.env.API_BASE_URL,
          auth_url: process.env.AUTH_URL,
          credentials: !!(process.env.API_USERNAME && process.env.API_PASSWORD),
          google_ai_available: !!process.env.GOOGLE_API_KEY,
          local_api_mode: process.env.LOCAL_API_MODE === "true",
          python_api_status: pythonApiStatus,
        });
      }
    } catch (error) {
      console.error("Error testing API connection:", error);
      res.status(500).json({
        status: "error",
        message:
          error instanceof Error
            ? error.message
            : "Unknown error testing connection",
        google_ai_available: !!process.env.GOOGLE_API_KEY,
      });
    }
  };

  app.get("/api/test-connection", testConnectionHandler);

  // Add a specific endpoint to check Python API server status
  const pythonApiStatusHandler: RequestHandler = async (
    _req: Request,
    res: Response
  ) => {
    try {
      const pythonApiStatus = getServerStatus();
      const isLocalMode = process.env.LOCAL_API_MODE === "true";

      res.json({
        status: "success",
        local_api_mode: isLocalMode,
        python_api_status: pythonApiStatus,
        message: isLocalMode
          ? pythonApiStatus.running
            ? "Python API server is running"
            : "Python API server is not running"
          : "Local API mode is disabled",
      });
    } catch (error) {
      console.error("Error getting Python API status:", error);
      res.status(500).json({
        status: "error",
        message:
          error instanceof Error
            ? error.message
            : "Unknown error getting Python API status",
      });
    }
  };

  app.get("/api/python-api-status", pythonApiStatusHandler);

  const httpServer = createServer(app);
  return httpServer;
}
