
/**
 * API Integration Module
 * Handles communication with the external unified API service
 * Can be configured to use either local Python API server or public API
 */

import axios from "axios";
import dotenv from "dotenv";
import {
  ArtifactResponse,
  TextReportResponse,
  ChatMessage,
  ChatResponse,
  AMCEData,
  MindsetItem,
} from "@/app/interpreter/doc-chat-types/api-types";

// Load environment variables from .env.local file
dotenv.config({ path: '.env.local' });

// Define fallback values for environment variables if not present in .env
const API_USERNAME = process.env.API_USERNAME || "<EMAIL>";
const API_PASSWORD = process.env.API_PASSWORD || "wd8BPYz9e2kPnn";
const CLIENT_ID = process.env.CLIENT_ID || "MR5gS0QSe3boMNAtnR0t1t2ctNpzSHsd";
const CLIENT_SECRET =
  process.env.CLIENT_SECRET ||
  "c1GGYa9U7gbX5dvDR8pRHvmmY067InwLi0HYZMNXzKg9zn99RvNf13ibsDzT2jKV";
const AUTH_URL = process.env.AUTH_URL || "https://auth.subconscious.ai";
const AUDIENCE =
  process.env.AUDIENCE || "https://dev-5qhuxyzkmd8cku6i.us.auth0.com/api/v2/";

// API configuration with support for local Python API server
const PUBLIC_API_URL = "https://api.dev.subconscious.ai";
const LOCAL_API_URL = process.env.API_BASE_URL || "http://localhost:8080";

// Determine which API to use (local or public)
// If LOCAL_API_MODE is set to 'true' in .env, use the local API
const USE_LOCAL_API = process.env.LOCAL_API_MODE === "true";
const API_BASE_URL = USE_LOCAL_API ? LOCAL_API_URL : PUBLIC_API_URL;

// Flag to determine whether authentication is needed
// Local API server typically doesn't need Auth0 authentication
const USE_AUTH = !USE_LOCAL_API;

console.log(`API Mode: ${USE_LOCAL_API ? "LOCAL" : "PUBLIC"}`);
console.log(`API Base URL: ${API_BASE_URL}`);
console.log(`Authentication required: ${USE_AUTH}`);

// Token caching
let cachedToken: string | null = null;
let tokenExpiry: number = 0;

/**
 * Get authentication token with caching
 * Fetch a real token from the API
 */
export async function getAuthToken(): Promise<string> {
  // If using local API, return an empty token (typically no auth needed)
  if (!USE_AUTH) {
    console.log("Local API mode - no authentication needed");
    return "";
  }

  const currentTime = Date.now();

  // Return cached token if it's still valid
  if (cachedToken && tokenExpiry > currentTime) {
    console.log("Using cached token");
    return cachedToken;
  }

  console.log("Fetching new auth token");
  try {
    // Make request to get a new token
    // Create form data for authentication
    const formData = new URLSearchParams();
    formData.append("grant_type", "password");
    formData.append("username", API_USERNAME || "");
    formData.append("password", API_PASSWORD || "");
    formData.append("audience", AUDIENCE || "");
    formData.append("client_id", CLIENT_ID || "");
    formData.append("client_secret", CLIENT_SECRET || "");

    const response = await axios.post(
      `${AUTH_URL}/oauth/token`,
      formData.toString(), // Ensure it's properly stringified
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        timeout: 10000, // Add timeout to prevent long hangs
      }
    );

    if (response.status !== 200) {
      throw new Error(`Authentication failed: ${response.statusText}`);
    }

    const token = response.data.access_token;
    const expiresIn = response.data.expires_in || 3600; // Default to 1 hour

    // Cache the token
    cachedToken = token;
    tokenExpiry = currentTime + (expiresIn - 60) * 1000; // Refresh 1 minute early, convert to ms

    console.log("Successfully obtained new auth token");
    return token;
  } catch (error) {
    console.error("Error getting authentication token:", error);

    // Instead of throwing an error, create a temporary token
    console.warn("Using temporary token due to auth server error");

    // Generate a timestamped token for tracing purposes
    const tempToken = `temp-token-${Date.now()}`;

    // Cache this temporary token, but for a shorter time
    cachedToken = tempToken;
    tokenExpiry = currentTime + 300000; // Just 5 minutes, so we'll retry relatively soon

    return tempToken;
  }
}

/**
 * Get experiment details from the external API
 * Fetches run details using the v3 API
 */
export async function getExperimentDetails(runId: string): Promise<any> {
  console.log(`Getting experiment details for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.get(
      `${API_BASE_URL}/api/v3/runs/${runId}/details`,
      {
        headers,
        timeout: 15000, // Add timeout to prevent long hangs
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received experiment details");
      return {
        success: true,
        ...response.data,
      };
    } else {
      console.log("API returned non-success response for experiment details");
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error fetching experiment details:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error fetching experiment details";

    return {
      success: false,
      error: `Failed to fetch experiment details: ${errorMessage}`,
    };
  }
}

/**
 * Get experiment artifacts from the external API
 * Fetches real data from the API using the provided run_id
 */
export async function getExperimentArtifacts(
  runId: string
): Promise<ArtifactResponse> {
  console.log(`Getting experiment artifacts for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      experiment_definition_file: "",
      analytics_output_file: "",
      experiment_definition_data: {},
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint for artifacts
    const response = await axios.get(
      `${API_BASE_URL}/api/v3/runs/${runId}/artifacts`,
      {
        headers,
        timeout: 15000, // Add timeout to prevent long hangs
      }
    );

    if (response.status === 200 && response.data && response.data.success) {
      console.log("Successfully received artifact data");
      return response.data;
    } else {
      console.log("API returned non-success response for artifact data");

      // Return a fallback response
      return {
        success: false,
        experiment_definition_file: "",
        analytics_output_file: "",
        experiment_definition_data: {
          title: `Experiment ${runId}`,
          context: "No experiment context available",
          target_behavior: "Unknown target behavior",
          country: "Unknown",
          year: new Date().getFullYear(),
        },
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error fetching experiment artifacts:", error);

    // Return error information
    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error fetching experiment data";

    return {
      success: false,
      experiment_definition_file: "",
      analytics_output_file: "",
      experiment_definition_data: {},
      error: `Failed to fetch experiment data: ${errorMessage}`,
    };
  }
}

/**
 * Get processed experiment details from the external API
 * Fetches processed details using the v3 API
 */
export async function getProcessedExperimentDetails(
  runId: string
): Promise<any> {
  console.log(`Getting processed experiment details for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.get(
      `${API_BASE_URL}/api/v3/runs/${runId}/processed/details`,
      {
        headers,
        timeout: 15000, // Add timeout to prevent long hangs
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received processed experiment details");
      return {
        success: true,
        ...response.data,
      };
    } else {
      console.log(
        "API returned non-success response for processed experiment details"
      );
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error fetching processed experiment details:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error fetching processed experiment details";

    return {
      success: false,
      error: `Failed to fetch processed experiment details: ${errorMessage}`,
    };
  }
}

/**
 * Get processed AMCE data from the external API
 * Fetches AMCE data using the v3 API
 */
export async function getProcessedAMCEData(
  runId: string
): Promise<{ success: boolean; data?: AMCEData; error?: string }> {
  console.log(`Getting processed AMCE data for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.get(
      `${API_BASE_URL}/api/v3/runs/${runId}/processed/amce`,
      {
        headers,
        timeout: 15000, // Add timeout to prevent long hangs
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received processed AMCE data");
      return {
        success: true,
        data: response.data,
      };
    } else {
      console.log("API returned non-success response for processed AMCE data");
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error fetching processed AMCE data:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error fetching processed AMCE data";

    return {
      success: false,
      error: `Failed to fetch processed AMCE data: ${errorMessage}`,
    };
  }
}

/**
 * Get processed mindset data from the external API
 * Fetches mindset data using the v3 API
 */
export async function getProcessedMindsetData(
  runId: string
): Promise<{ success: boolean; data?: MindsetItem[]; error?: string }> {
  console.log(`Getting processed mindset data for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.get(
      `${API_BASE_URL}/api/v3/runs/${runId}/processed/mindset`,
      {
        headers,
        timeout: 15000, // Add timeout to prevent long hangs
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received processed mindset data");
      return {
        success: true,
        data: response.data,
      };
    } else {
      console.log(
        "API returned non-success response for processed mindset data"
      );
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error fetching processed mindset data:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error fetching processed mindset data";

    return {
      success: false,
      error: `Failed to fetch processed mindset data: ${errorMessage}`,
    };
  }
}

/**
 * Get a single artifact file by filename
 * Fetches a specific artifact file using the v3 API
 */
export async function getSingleArtifact(fileName: string): Promise<any> {
  console.log(`Getting single artifact: ${fileName}`);

  // Check if fileName is valid (must be non-empty)
  if (!fileName || fileName.trim() === "") {
    return {
      success: false,
      error: "Invalid file name provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.get(
      `${API_BASE_URL}/api/v3/artifacts/${fileName}`,
      {
        headers,
        timeout: 15000, // Add timeout to prevent long hangs
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received artifact file");
      return {
        success: true,
        data: response.data,
      };
    } else {
      console.log("API returned non-success response for artifact file");
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error fetching artifact file:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error fetching artifact file";

    return {
      success: false,
      error: `Failed to fetch artifact file: ${errorMessage}`,
    };
  }
}

/**
 * Generate experiment summary using the external API
 * Uses the v3 API to generate a summary
 */
export async function generateExperimentSummaryFromAPI(
  runId: string
): Promise<any> {
  console.log(`Generating experiment summary for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.post(
      `${API_BASE_URL}/api/v3/runs/${runId}/generate/summary`,
      {}, // Empty body
      {
        headers,
        timeout: 30000, // Longer timeout for generation
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received generated experiment summary");
      return {
        success: true,
        summary: response.data.summary || "",
        ...response.data,
      };
    } else {
      console.log(
        "API returned non-success response for experiment summary generation"
      );
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error generating experiment summary:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error generating experiment summary";

    return {
      success: false,
      error: `Failed to generate experiment summary: ${errorMessage}`,
    };
  }
}

/**
 * Generate causal sentences using the external API
 * Uses the v3 API to generate causal sentences
 */
export async function generateCausalSentencesFromAPI(
  runId: string
): Promise<any> {
  console.log(`Generating causal sentences for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.post(
      `${API_BASE_URL}/api/v3/runs/${runId}/generate/causal-sentences`,
      {}, // Empty body
      {
        headers,
        timeout: 30000, // Longer timeout for generation
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received generated causal sentences");
      return {
        success: true,
        sentences: response.data.sentences || [],
        ...response.data,
      };
    } else {
      console.log(
        "API returned non-success response for causal sentences generation"
      );
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error generating causal sentences:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error generating causal sentences";

    return {
      success: false,
      error: `Failed to generate causal sentences: ${errorMessage}`,
    };
  }
}

/**
 * Generate mindset sentences using the external API
 * Uses the v3 API to generate mindset sentences
 */
export async function generateMindsetSentencesFromAPI(
  runId: string
): Promise<any> {
  console.log(`Generating mindset sentences for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();
    console.log("Obtained token for API request");

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Use the v3 API endpoint
    const response = await axios.post(
      `${API_BASE_URL}/api/v3/runs/${runId}/generate/mindset-sentences`,
      {}, // Empty body
      {
        headers,
        timeout: 30000, // Longer timeout for generation
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received generated mindset sentences");
      return {
        success: true,
        sentences: response.data.sentences || [],
        ...response.data,
      };
    } else {
      console.log(
        "API returned non-success response for mindset sentences generation"
      );
      return {
        success: false,
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error generating mindset sentences:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error generating mindset sentences";

    return {
      success: false,
      error: `Failed to generate mindset sentences: ${errorMessage}`,
    };
  }
}

/**
 * Generate causal insights report using the experiment data
 * This function is used by the routes.ts file
 */
export async function generateCausalInsightsReport(
  runId: string
): Promise<TextReportResponse> {
  console.log(`Generating causal insights report for run ID: ${runId}`);

  // Check if run_id is valid (must be non-empty)
  if (!runId || runId.trim() === "") {
    return {
      success: false,
      report_text: "",
      error: "Invalid run ID provided",
    };
  }

  try {
    // Get auth token
    const token = await getAuthToken();

    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Make request to generate report
    const response = await axios.post(
      `${API_BASE_URL}/api/v3/runs/${runId}/report`,
      { format: "text" },
      {
        headers,
        timeout: 30000, // Longer timeout for report generation
      }
    );

    if (response.status === 200 && response.data) {
      console.log("Successfully received report from API");
      // If the response doesn't have a success field, assume it's successful
      if (response.data.success === undefined) {
        return {
          success: true,
          report_text: response.data.report_text || response.data.toString(),
          error: undefined
        };
      }
      return response.data;
    } else {
      console.log("API returned non-success response for report generation");
      return {
        success: false,
        report_text: "",
        error: `API returned status ${response.status}`,
      };
    }
  } catch (error) {
    console.error("Error generating causal insights report:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error generating report";

    return {
      success: false,
      report_text: "",
      error: `Failed to generate report: ${errorMessage}`,
    };
  }
}

/**
 * Process experiment chat with context
 * This function is used by the routes.ts file
 * Uses only the external API with no local fallback
 */
export async function processExperimentChat(
  runId: string,
  userMessage: string,
  previousMessages: ChatMessage[] = []
): Promise<ChatResponse> {
  try {
    // Input validation
    if (!runId?.trim() || !userMessage?.trim()) {
      throw new Error("Invalid run ID or empty message");
    }

    const messages = [...previousMessages];
    messages.push({ role: "user", content: userMessage });

    // Get auth token
    const token = await getAuthToken();
    
    // Build headers with optional Authorization
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Only add Authorization header when needed and token is available
    if (USE_AUTH && token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    console.log(`Sending chat request to external API: ${API_BASE_URL}/api/v3/chat`);

    // Direct API call to external service
    const response = await axios.post(
      `${API_BASE_URL}/api/v3/chat`,
      { run_id: runId, messages },
      {
        headers,
        timeout: 30000,
        validateStatus: status => status === 200
      }
    );

    if (!response.data) {
      throw new Error("Empty response from API");
    }

    console.log("Successfully received chat response from external API");
    return response.data;
  } catch (error) {
    // Enhanced error handling with specific error messages
    let errorMessage = "Unknown error occurred";
    
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    if (axios.isAxiosError(error)) {
      errorMessage = error.response?.data?.message || error.message;
    }

    console.error("Chat processing error:", errorMessage);
    
    return {
      success: false,
      message: {
        role: "assistant",
        content: `I'm having trouble processing your request. ${errorMessage}`
      },
      error: errorMessage
    };
  }
}
