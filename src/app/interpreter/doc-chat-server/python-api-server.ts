/**
 * Python API Server Integration
 * This module manages starting and monitoring the Python API server
 */

import { execSync, spawn, ChildProcess } from "child_process";
import { log } from "./logger";
import * as fs from "fs";
import * as path from "path";

// Get Python API port from environment variable or use default
const PYTHON_API_PORT = process.env.PYTHON_API_PORT || 8080;

// Path to the Python API file
const PYTHON_API_PATH = path.resolve("./doc-chat/unified_api/unified_api.py");

// Flag to track server state
let pythonServerProcess: ChildProcess | null = null;
let serverStarted = false;
let startupAttempts = 0;
const MAX_STARTUP_ATTEMPTS = 3;

/**
 * Check if Python is installed and available
 */
function isPythonAvailable(): boolean {
  try {
    const pythonVersion = execSync(
      "python3 --version || python --version"
    ).toString();
    log(`Python detected: ${pythonVersion.trim()}`, "python-api");
    return true;
  } catch (error) {
    log(
      "Python is not available. Please install Python 3.x to use the local API server.",
      "python-api"
    );
    return false;
  }
}

/**
 * Check if the required Python packages are installed
 */
function arePythonDependenciesInstalled(): boolean {
  try {
    // Check for key dependencies
    execSync(
      "pip show fastapi uvicorn aiohttp cachetools pydantic openai reportlab"
    );
    log("Required Python packages are installed", "python-api");
    return true;
  } catch (error) {
    log("Some required Python packages are missing", "python-api");
    return false;
  }
}

/**
 * Install required Python packages
 */
function installPythonDependencies(): boolean {
  try {
    log("Installing required Python packages...", "python-api");
    execSync(
      "pip install fastapi uvicorn aiohttp cachetools pydantic openai reportlab requests"
    );
    log("Successfully installed required Python packages", "python-api");
    return true;
  } catch (error) {
    log(`Failed to install Python packages: ${error}`, "python-api");
    return false;
  }
}

/**
 * Check if the API file exists
 */
function doesApiFileExist(): boolean {
  if (fs.existsSync(PYTHON_API_PATH)) {
    log(`Found Python API file at: ${PYTHON_API_PATH}`, "python-api");
    return true;
  } else {
    log(`Python API file not found at: ${PYTHON_API_PATH}`, "python-api");
    return false;
  }
}

/**
 * Start the Python API server
 */
export function startPythonApiServer(): boolean {
  // Don't try to start if we've already tried too many times
  if (startupAttempts >= MAX_STARTUP_ATTEMPTS) {
    log(
      `Maximum startup attempts (${MAX_STARTUP_ATTEMPTS}) reached. Giving up.`,
      "python-api"
    );
    return false;
  }

  startupAttempts++;

  // Check prerequisites
  if (!isPythonAvailable()) {
    return false;
  }

  if (!doesApiFileExist()) {
    return false;
  }

  if (!arePythonDependenciesInstalled()) {
    if (!installPythonDependencies()) {
      return false;
    }
  }

  // Don't start if already running
  if (pythonServerProcess && !pythonServerProcess.killed) {
    log("Python API server is already running", "python-api");
    return true;
  }

  try {
    // Start the Python server
    log("Starting Python API server...", "python-api");

    // Use python3 if available, fall back to python
    const pythonCommand = "python3";

    // Start the server process
    const process = spawn(pythonCommand, [PYTHON_API_PATH]);
    pythonServerProcess = process;

    // Set up event handlers for the process
    process.stdout?.on("data", (data) => {
      const output = data.toString().trim();
      log(`[Python API] ${output}`, "python-api");

      // Check for server startup completion
      if (
        output.includes("Application startup complete") ||
        output.includes("Uvicorn running on http://0.0.0.0:8080")
      ) {
        serverStarted = true;
        log("Python API server successfully started", "python-api");
      }
    });

    process.stderr?.on("data", (data) => {
      log(`[Python API Error] ${data.toString().trim()}`, "python-api");
    });

    pythonServerProcess.on("close", (code) => {
      log(`Python API server process exited with code ${code}`, "python-api");
      pythonServerProcess = null;
      serverStarted = false;
    });

    // Wait longer to make sure server starts (especially on slower systems)
    setTimeout(() => {
      if (!serverStarted) {
        log(
          "Python API server startup timeout - may still be starting up",
          "python-api"
        );
      }
    }, 15000);

    return true;
  } catch (error) {
    log(`Failed to start Python API server: ${error}`, "python-api");
    return false;
  }
}

/**
 * Stop the Python API server if it's running
 */
export function stopPythonApiServer(): void {
  if (pythonServerProcess && !pythonServerProcess.killed) {
    log("Stopping Python API server...", "python-api");
    pythonServerProcess.kill();
    log("Python API server stopped", "python-api");
  } else {
    log("Python API server is not running", "python-api");
  }
}

/**
 * Check if the Python API server is running and responding
 */
export async function isPythonApiServerRunning(): Promise<boolean> {
  try {
    log("Checking if Python API server is running...", "python-api");
    const response = await fetch(
      `http://127.0.0.1:${PYTHON_API_PORT}/api/test-connection`
    ); // Use 127.0.0.1
    log(
      `Python API server responded with status ${response.status}`,
      "python-api"
    );
    if (response.ok) {
      log("Python API server is running and responding", "python-api");
      // Update the serverStarted flag when we detect the server is running
      serverStarted = true;
      return true;
    } else {
      log(`Python API server returned status ${response.status}`, "python-api");
      serverStarted = false;
      return false;
    }
  } catch (error) {
    log(
      `Python API server is not running or not responding: ${error}`,
      "python-api"
    );
    log(`Error: ${error}`, "python-api");
    serverStarted = false;
    return false;
  }
}

/**
 * Ensure the Python API server is running, try to start it if needed
 */
export async function ensurePythonApiServerRunning(): Promise<boolean> {
  // First check if server is already running
  const isRunning = await isPythonApiServerRunning();
  if (isRunning) {
    return true;
  }

  // If not running, try to start it
  log(
    "Python API server not detected, attempting to start it...",
    "python-api"
  );
  const startResult = startPythonApiServer();

  if (startResult) {
    // Wait longer for server to start on slower systems
    log("Waiting for Python API server to start...", "python-api");
    await new Promise((resolve) => setTimeout(resolve, 25000)); // Increased wait time

    // Wait an additional 5 seconds before checking if the server is running
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Check again if server is running
    return await isPythonApiServerRunning();
  }

  return false;
}

// Export a function to get server status
export function getServerStatus(): {
  running: boolean;
  startupAttempts: number;
  maxAttempts: number;
} {
  return {
    running: serverStarted,
    startupAttempts,
    maxAttempts: MAX_STARTUP_ATTEMPTS,
  };
}
