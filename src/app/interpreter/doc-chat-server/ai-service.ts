/**
 * AI Service for Causal Insights Platform
 * Using Google's Generative AI (Gemini) for report generation
 */

import {
  GoogleGenerativeAI,
  HarmCategory,
  HarmBlockThreshold,
} from "@google/generative-ai";
import dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: '.env.local' });

// Get API key from environment
const API_KEY = process.env.GOOGLE_API_KEY;

// Log API key presence (not the actual key)
console.log(`Google API Key available: ${!!API_KEY}`);
console.log(`API Key: ${API_KEY ? API_KEY.substring(0, 5) + '...' : 'undefined'}`);

// Initialize Google AI
const genAI = new GoogleGenerativeAI(API_KEY || "");

// Configure safety settings
const safetySettings = [
  {
    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
    threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
  },
  {
    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
  },
  {
    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
  },
  {
    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
  },
];

// Model configuration
const modelConfig = {
  temperature: 0.2,
  topK: 40,
  topP: 0.95,
  maxOutputTokens: 2048,
};

/**
 * Generate causal insights report summary using Google's Gemini AI
 */
export async function generateExperimentSummary(
  experimentTitle: string,
  context: string,
  targetBehavior: string,
  country: string,
  year: number | string,
  amceData: any[],
  mindsetData: any[] = []
): Promise<string> {
  // Format the title by replacing underscores with spaces and capitalizing words
  experimentTitle = experimentTitle
    .replace(/_/g, " ")
    .split(" ")
    .map(
      (word: string) =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    .join(" ");
  if (!API_KEY) {
    console.warn(
      "No Google API key provided. Using template-based report generation."
    );
    console.error("GOOGLE_API_KEY environment variable is not set. Please check your .env file.");
    return generateTemplateBasedReport(
      experimentTitle,
      context,
      targetBehavior,
      country,
      year,
      amceData,
      []
    );
  }

  try {
    // Format AMCE data for the prompt
    const formattedAmceData =
      amceData && amceData.length > 0
        ? amceData
            .map(
              (item) =>
                `${item.attribute_text || "Unknown Attribute"}: ${item.level_text || "Unknown Level"} (AMCE: ${
                  typeof item.AMCE === "number" ? item.AMCE.toFixed(2) : "N/A"
                })`
            )
            .join("\n")
        : "No AMCE data available";

    // Create the model
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-pro",
      safetySettings,
      generationConfig: modelConfig,
    });

    // Build the prompt
    const prompt = `
    # Generate Causal Insights Report

    ## Experiment Details
    - Title: ${experimentTitle}
    - Context: ${context}
    - Target Behavior: ${targetBehavior}
    - Location: ${country}
    - Year: ${year}

    ## AMCE Data (Average Marginal Component Effect)
    ${formattedAmceData}

    ## Instructions
    Create a comprehensive causal insights report for this experiment. The report should include:
    1. A brief introduction summarizing the experiment and its purpose
    2. An explanation of the methodology used (conjoint analysis with AMCE)
    3. A detailed analysis of the key findings, focusing on the elements with the highest AMCE values
    4. Conclusions about which factors have the strongest causal impact on the target behavior
    5. Recommendations for practical applications of these findings
    6. Suggestions for potential follow-up experiments

    Format the report with Markdown headings and bullet points for readability.
    `;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    return text;
  } catch (error) {
    console.error("Error generating AI report:", error);
    return generateTemplateBasedReport(
      experimentTitle,
      context,
      targetBehavior,
      country,
      year,
      amceData,
      []
    );
  }
}

/**
 * Generate a response to a user query about the experiment using Google's Gemini AI
 */
export async function generateAIResponseToQuery(
  query: string,
  experimentData: any,
  reportText: string,
  mindsetData: any[] = [],
  amceData: any[] = []
): Promise<string> {
  if (!API_KEY) {
    console.warn(
      "No Google API key provided. Using template-based response generation."
    );
    console.error("GOOGLE_API_KEY environment variable is not set. Please check your .env file.");
    return "I'm sorry, but I need a Google API key to provide AI-generated responses to your questions. Please contact the administrator to set up the API key.";
  }
  
  // Check if report text is empty or too short
  if (!reportText || reportText.length < 50) {
    console.error("Report text is empty or too short:", reportText);
    return "The provided report does not contain sufficient information to answer your query. Please ensure a valid experiment report is generated first.";
  }

  try {
    // Create the model
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-flash",
      safetySettings,
      generationConfig: {
        ...modelConfig,
        maxOutputTokens: 1024,
      },
    });

    // Format experiment data
    // Format the title by replacing underscores with spaces and capitalizing words
    let title = experimentData.title || "Unknown";
    title = title
      .replace(/_/g, " ")
      .split(" ")
      .map(
        (word: string) =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      )
      .join(" ");

    const experimentDetails = `
    # Experiment Context
    - Title: ${title}
    - Context: ${experimentData.context || "Not provided"}
    - Target Behavior: ${experimentData.target_behavior || "Not specified"}
    - Location: ${experimentData.country || "Unknown"}, ${experimentData.state || "Unknown"}
    - Year: ${experimentData.year || "Unknown"}
    `;

    // Build the prompt
    const prompt = `
    ${experimentDetails}

    # Causal Insights Report
    ${reportText}

    # User Query
    ${query}

    # Instructions
    Based on the experiment details and causal insights report above, please provide a helpful, accurate, and concise response to the user's query. Focus specifically on answering what they asked about regarding this causal experiment.

    Use a friendly, professional tone and include specific data points from the report when relevant. If the information needed to answer the query is not available in the provided context, acknowledge that limitation rather than making up information.
    `;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    return text;
  } catch (error) {
    console.error("Error generating AI response:", error);
    // Provide a more helpful error message when the report doesn't contain the information
    if (error instanceof Error) {
      if (error.message.includes("content safety")) {
        return "I'm sorry, but I cannot provide an answer to that query due to content safety restrictions.";
      } else if (error.message.includes("not found") || error.message.includes("no information")) {
        return "The provided report does not contain information to answer your query. Please ask about something related to the experiment data or try rephrasing your question.";
      } else {
        return `I'm sorry, I encountered an error processing your request. ${error.message}`;
      }
    } else {
      return "I'm sorry, I encountered an error processing your request. Please try again.";
    }
  }
}

/**
 * Fallback template-based report generation when API key is not available
 */
function generateTemplateBasedReport(
  experimentTitle: string,
  context: string,
  targetBehavior: string,
  country: string,
  year: number | string,
  amceData: any[],
  mindsetData: any[] = []
): string {
  // Format the title by replacing underscores with spaces and capitalizing words
  experimentTitle = experimentTitle
    .replace(/_/g, " ")
    .split(" ")
    .map(
      (word: string) =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    .join(" ");
  // Format AMCE data
  let amceListText = "No AMCE data available.";

  if (amceData && amceData.length > 0) {
    // Sort by AMCE value (highest first)
    const sortedAmce = [...amceData].sort((a, b) => {
      const amceA = typeof a.AMCE === "number" ? a.AMCE : 0;
      const amceB = typeof b.AMCE === "number" ? b.AMCE : 0;
      return amceB - amceA;
    });

    amceListText = sortedAmce
      .map((item, index) => {
        const amceValue =
          typeof item.AMCE === "number" ? item.AMCE.toFixed(2) : "N/A";
        return `${index + 1}. ${item.attribute_text || "Unknown Attribute"}: ${item.level_text || "Unknown Level"} (AMCE: ${amceValue})`;
      })
      .join("\n");
  }

  // Build the report
  return `# Causal Insights Report: ${experimentTitle}

## Experiment Context

${context || "This experiment examines causal relationships between variables to understand impact and effectiveness."}

Target Behavior: ${targetBehavior || "Not specified"}
Location: ${country || "Not specified"}
Year: ${year || new Date().getFullYear()}

## Methodology

This experiment used conjoint analysis methodology, where multiple elements are tested simultaneously in a randomized design. The approach systematically varies different elements and measures their individual and combined effects on outcomes.

The impact of each element was measured using AMCE (Average Marginal Component Effect), which quantifies the causal effect of each feature compared to a baseline.

## Key Findings

The experiment measured effects using AMCE (Average Marginal Component Effect):

${amceListText}

${mindsetData && mindsetData.length > 0 ? `## Mindset Segments

The experiment identified different mindset segments with varying preferences:

${mindsetData.map((item, index) => `${index + 1}. ${item.name || `Segment ${index + 1}`}: ${item.description || 'No description available'}`).join('\n')}
` : ''}

## Conclusion

This causal analysis helps identify which factors have the strongest impact on ${targetBehavior || "the target outcomes"}. The findings can guide decision-making by focusing on the most impactful changes.

## Next Steps

- Consider implementing changes based on the most significant positive effects
- Further explore any unexpected findings
- Plan follow-up experiments to validate key insights
`;
}
