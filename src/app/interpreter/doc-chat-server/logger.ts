/**
 * Simple logger for the doc-chat server
 */

// Define log levels
export type LogLevel = "info" | "warn" | "error" | "debug";

/**
 * Log a message with a specific tag and level
 */
export function log(
  message: string,
  tag: string = "server",
  level: LogLevel = "info"
): void {
  const timestamp = new Date().toISOString();
  const formattedTag = tag ? `[${tag}]` : "";

  switch (level) {
    case "info":
      console.log(`${timestamp} ${formattedTag} ${message}`);
      break;
    case "warn":
      console.warn(`${timestamp} ${formattedTag} ${message}`);
      break;
    case "error":
      console.error(`${timestamp} ${formattedTag} ${message}`);
      break;
    case "debug":
      console.debug(`${timestamp} ${formattedTag} ${message}`);
      break;
  }
}
