/**
 * Standard server entry point for non-Replit environments
 * This file sets up a separate Express server for the backend without Replit-specific dependencies
 */

import express from "express";
import { createServer } from "http";
import path from "path";
import { registerRoutes } from "./routes";
import { log } from "./vite";
import dotenv from "dotenv";
import { ensurePythonApiServerRunning } from "./python-api-server";

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const server = createServer(app);

// Configure middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Set port
const PORT = process.env.PORT || 3000;

// Add routes
async function startServer() {
  try {
    // Start Python API server if needed
    if (process.env.LOCAL_API_MODE === "true") {
      log("LOCAL_API_MODE is enabled. Starting Python API server...");
      await ensurePythonApiServerRunning();
    }

    // Register API routes
    await registerRoutes(app);

    // Start server
    server.listen(PORT, () => {
      log(`API server running on http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Start the server
startServer();

// Handle graceful shutdown
process.on("SIGTERM", () => {
  log("SIGTERM received, shutting down gracefully");
  server.close(() => {
    log("Server closed");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  log("SIGINT received, shutting down gracefully");
  server.close(() => {
    log("Server closed");
    process.exit(0);
  });
});
