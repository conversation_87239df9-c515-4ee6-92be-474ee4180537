import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { ensurePythonApiServerRunning } from "./python-api-server";
import { createProxyMiddleware } from "http-proxy-middleware";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Proxy specific API requests to the Python API server
app.use(
  "/api/get-experiment-artifact",
  createProxyMiddleware({
    target: "http://localhost:8080",
    changeOrigin: true,
  })
);

app.use(
  "/api/generate-report",
  createProxyMiddleware({
    target: "http://localhost:8080",
    changeOrigin: true,
  })
);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Try to start the Python API server if configured to use local mode
  if (process.env.LOCAL_API_MODE === "true") {
    log("LOCAL_API_MODE is enabled. Starting Python API server...");
    try {
      const result = await ensurePythonApiServerRunning();
      if (result) {
        log("Python API server started successfully");
      } else {
        log(
          "Failed to start Python API server. API functionality may be limited"
        );
      }
    } catch (error) {
      log(`Error starting Python API server: ${error}`);
    }
  } else {
    log("LOCAL_API_MODE is disabled. Using remote API endpoints");
  }

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000 for Replit environment
  // this serves both the API and the client.
  // In non-Replit environments, use PORT environment variable or port 3000
  const port = 4000;

  // Bind to the port immediately in Replit environment
  server.listen(
    {
      port,
      host: "0.0.0.0",
      reusePort: true,
    },
    () => {
      log(`serving on port ${port}`);
    }
  );
})();
