import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { type Server } from "http";
import { nanoid } from "nanoid";

// Simplified version without Vite dependencies
// This is a placeholder that provides the same exports but doesn't use Vite

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

// Simplified setupVite function that doesn't actually use Vite
export async function setupVite(app: Express, server: Server) {
  log("Vite setup skipped - using external backend API", "vite");

  // Add a simple middleware to handle requests
  app.use("*", async (req, res) => {
    res
      .status(200)
      .set({ "Content-Type": "text/html" })
      .end(
        "<html><body><h1>Frontend Only Mode</h1><p>This frontend is configured to use an external backend API.</p></body></html>"
      );
  });
}

// Simplified serveStatic function
export function serveStatic(app: Express) {
  log("Static file serving skipped - using external backend API", "vite");

  // Add a simple middleware to handle requests
  app.use("*", (_req, res) => {
    res
      .status(200)
      .set({ "Content-Type": "text/html" })
      .end(
        "<html><body><h1>Frontend Only Mode</h1><p>This frontend is configured to use an external backend API.</p></body></html>"
      );
  });
}
