/**
 * Server initialization for doc-chat
 * This module provides functions to initialize the doc-chat server components
 */

import { log } from "./logger";

let initialized = false;

/**
 * Initialize the doc-chat server components
 * This function should be called when the application starts
 */
export async function initDocChatServer(): Promise<boolean> {
  if (initialized) {
    log("Doc-chat server already initialized", "init");
    return true;
  }

  log("Initializing doc-chat server components", "init");

  // No Python API server initialization - removed as it's handled by separate backend

  initialized = true;
  log("Doc-chat server initialized successfully", "init");
  return true;
}

/**
 * Shutdown the doc-chat server components
 * This function should be called when the application stops
 */
export function shutdownDocChatServer(): void {
  if (!initialized) {
    log("Doc-chat server not initialized, nothing to shutdown", "init");
    return;
  }

  log("Shutting down doc-chat server components", "init");

  // No Python API server shutdown - removed as it's handled by separate backend

  initialized = false;
  log("Doc-chat server shutdown complete", "init");
}
