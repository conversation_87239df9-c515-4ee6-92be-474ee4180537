"use client";
import React from "react";
import { useContext, useEffect, useState } from "react";
import StatusCard from "./_components/_home/StatusCard";
import { useUser, withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import SessionContext from "./_components/_util/SessionContext";
import filterRuns from "./api/util/filter_runs";
import * as Sentry from "@sentry/react";
import { Header } from "./_components/_ui/NotificationCenter";
import { serializeData } from "@/app/utils/serialization"; // Import serializeData

import { Sparkle } from "lucide-react";
import {
  HelperCard1,
  HelperCard3,
  HelperCard2,
} from "../../public/icons/NoExperimentIcon";

const fetcher = async (uri: string, userID: string | null) => {
  const startTime = Date.now();
  try {
    const response = await fetch(uri, {
      method: "POST",
      body: JSON.stringify({ userID: userID }),
    });

    const duration = Date.now() - startTime;
    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return data;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Log error with contextual information
    Sentry.captureException(error, {
      tags: {
        api_endpoint: uri,
        user_id: userID,
        status: "error",
      },
      extra: {
        response_time: `${duration}ms`,
        response_status: (error as any).response
          ? (error as any).response.status
          : "unknown",
        error_message: (error as any).message,
      },
    });

    throw error;
  }
};

export default withPageAuthRequired(
  function Home() {
    const { runs, setRuns } = useContext(SessionContext);

    const { user } = useUser();
    const [experimentsCompleted, setExperimentsCompleted] = useState<number>(
      runs.length
    );

    const [localRuns, setLocalRuns] = useState<any[] | undefined>(undefined);
    const [taskCount, setTaskCount] = useState<number>(0);

    const isCustomer = (
      user?.["https://www.api.subconscious.ai/roles"] as string[]
    )?.includes("customer");

    useEffect(() => {
      if (user?.sub !== undefined) {
        fetcher("/api/runs", user?.sub)
          .then((fetchRuns) => {
            // Ensure fetchRuns is an array and serialize it immediately
            const runsArray = Array.isArray(fetchRuns) ? serializeData(fetchRuns) : [];
            setExperimentsCompleted(runsArray.length);
            setLocalRuns(runsArray);
          })
          .catch((error) => {
            console.error("Error fetching runs:", error);
            Sentry.captureException(error);
            // Set empty array on error
            setLocalRuns([]);
          });
      }
    }, [user]);

    const getWelcomeMessage = () => {
      if (isCustomer) {
        return "Thank you for being a premium member! 🎉";
      }
      if (runs.length > 0) {
        return "Welcome to Subconscious AI 🎉";
      }
      return "Enjoy 2 free experiments as a welcome gift 🎉";
    };

    useEffect(() => {
      // Serialize the data after filterRuns but before setRuns
      const filteredRuns = filterRuns(localRuns);
      const serializedRuns = serializeData(filteredRuns);
      setRuns(serializedRuns as any); // Cast the result to any to avoid TypeScript errors
    }, [localRuns, setRuns]);

    useEffect(() => {
      let totalTaskCount = 0;
      runs.forEach((run: any) => {
        totalTaskCount += run.task_count;
      });
      setTaskCount(totalTaskCount);
    }, [runs]);

    const ReplayTutorials = () => {
      if (window.pendo && window.pendo.isReady) {
        window.pendo.showGuideById("Umu_egx0ppueAgBpqlE8l2JkjSY");
      }
    };

    return (
      <>
        <div suppressHydrationWarning={true} className="z-10 py-8 px-10 w-full flex flex-col gap-8 justify-between font-inter">
          {/* DIV FOR DASHBOARD + BUTTON*/}
          <div suppressHydrationWarning={true} className="flex justify-between">
            <h1 className="text-text-dark font-medium text-3xl">Dashboard</h1>

            {/* IDEAS PORTAL BUTTON*/}
              <div suppressHydrationWarning={true} className="flex gap-2">
              <button
                onClick={ReplayTutorials}
                className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
              >
                Show Tutorial
              </button>
              <div suppressHydrationWarning={true} className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
                <Header />
              </div>
            </div>
          </div>

          {/* CONTENT */}
          <div suppressHydrationWarning={true} className="flex flex-col gap-6 w-full">
            {/* WELCOME MESSAGE */}
            <div suppressHydrationWarning={true} className="flex flex-col gap-2.5 w-full bg-white rounded-xl p-10 border border-card-border border-opacity-70">
              {isCustomer && (
                <h2 className="font-inter text-text-dark font-medium text-4xl">
                  Thank you for being a premium member! 🎉
                </h2>
              )}
              {!isCustomer && runs.length > 0 && (
                <h2 className="font-inter text-text-dark font-medium text-4xl">
                  Welcome to Subconscious AI 🎉
                </h2>
              )}
              {!isCustomer && runs.length === 0 && (
                <h2 className="font-inter text-text-dark font-medium text-4xl">
                  Enjoy 2 free experiments as a welcome gift 🎉
                </h2>
              )}

              <p className="font-roboto text-text-dark text-xl font-normal">
                We are delighted to have you on board! Below are a few pointers
                to get you started.
              </p>
            </div>

            {/* HELPER CARDS */}

            <div suppressHydrationWarning={true} className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
              <a
                href="https://discord.gg/3bgj4ZhABz"
                target="_blank"
                rel="discord"
              >
                <div
                  suppressHydrationWarning={true}
                  className="flex flex-col justify-between items-center text-center border border-card-border rounded-xl h-full"
                  style={{ backgroundColor: "#EFF0F5" }}
                >
                  <HelperCard1 />
                  <div suppressHydrationWarning={true} className="pl-8 pr-8 pb-8">
                    <p className="text-text-dark font-medium text-xl">
                      Join Our Discord
                    </p>
                    <p className="text-text-dark font-small">
                      Be a part of our community and get support
                    </p>
                  </div>
                </div>
              </a>
              <a href="/showcase" rel="Showcase module">
                <div
                  suppressHydrationWarning={true}
                  className="flex flex-col justify-between items-center text-center border border-card-border rounded-xl h-full"
                  style={{ backgroundColor: "#EFF0F5" }}
                >
                  <HelperCard2 />
                  <div suppressHydrationWarning={true} className="pl-8 pr-8 pb-8">
                    <p className="text-text-dark font-medium text-xl">
                      Explore Showcase
                    </p>
                    <p className="text-text-dark font-small">
                      See how we prove bioequivalence
                    </p>
                  </div>
                </div>
              </a>
              <a href="/ideation" rel="Ideation module">
                <div
                  suppressHydrationWarning={true}
                  className="flex flex-col justify-between items-center text-center border border-card-border rounded-xl h-full"
                  style={{ backgroundColor: "#EFF0F5" }}
                >
                  <HelperCard3 />
                  <div suppressHydrationWarning={true} className="pl-8 pr-8 pb-8">
                    <p className="text-text-dark font-medium text-xl">
                      Run Your Own Experiment
                    </p>
                    <p className="text-text-dark font-small">
                      Find the WHY behind a decision
                    </p>
                  </div>
                </div>
              </a>
            </div>

            {/* EXPERIMENT STATS */}
            <div suppressHydrationWarning={true}>
              <h2 className="font-inter font-medium text-lg text-subtitle pb-3">
                Experiment Results
              </h2>
              <div suppressHydrationWarning={true} className="flex flex-row gap-5 w-full">
                <StatusCard
                  icon={"/exp_completed_icon.svg"}
                  iconAlt="Magnifying Glass Icon"
                  statTitle={"Experiments completed"}
                  count={runs.length}
                />
                <StatusCard
                  icon={"/users_surveyed_icon.svg"}
                  iconAlt="Group of People Icon"
                  statTitle={"Survey questions answered"}
                  count={taskCount}
                />
              </div>
            </div>
          </div>
        </div>
      </>
    );
  },
  {
    returnTo: "/",
  }
);
