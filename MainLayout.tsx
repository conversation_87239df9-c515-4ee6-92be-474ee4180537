"use client";
import React, { ReactNode, useState, useEffect } from "react";
import Navigation from "@/app/_components/_layout/Navigation";
import MobileView from "@/app/_components/_mobile/MobileView";
import { usePathname } from "next/navigation";
// import { HydrationSuppressor } from "@/components/HydrationSuppressor";

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [isMobileView, setIsMobileView] = useState<boolean>(false);
  const pathname = usePathname();

  // Check if the current path is the interpreter page
  const isInterpreterPage =
    pathname === "/interpreter" || (pathname && pathname.startsWith("/interpreter/")) || false;

  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth <= 768); // Adjust this threshold as needed
    };

    // Initial check
    handleResize();

    // Event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div suppressHydrationWarning className="flex flex-row w-full h-screen overflow-hidden">
      {isMobileView ? (
        <MobileView /> // Render MobileView component if isMobileView is true
      ) : (
        <>
          {!isInterpreterPage && <Navigation />}
          <div
            suppressHydrationWarning
            className={`w-full h-fill mt-3 flex-col bg-background overflow-y-auto ${
              isInterpreterPage ? "" : ""
            }`}
            style={{
              borderTopLeftRadius: isInterpreterPage ? "0px" : "24px",
            }}
          >
            <div suppressHydrationWarning className="h-full overflow-auto">{children}</div>
          </div>
        </>
      )}
    </div>
  );
};

export default MainLayout;
